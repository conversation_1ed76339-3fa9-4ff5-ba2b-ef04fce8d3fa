#!/usr/bin/env python3
"""
Pydantic models for the FastAPI image generation service.
These models match the Swift implementation structure.
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List
from enum import Enum


class GenerationRequest(BaseModel):
    """Request model for image generation"""
    prompt: str = Field(..., description="Text prompt to guide image generation")
    negativePrompt: str = Field("", description="Negative text prompt to guide what to avoid")
    imageCount: int = Field(1, ge=1, le=4, description="Number of images to generate (1-4)")
    stepCount: int = Field(50, ge=1, le=100, description="Number of inference steps (1-100)")
    seed: int = Field(0, ge=0, description="Random seed for reproducible results")
    guidanceScale: float = Field(4.5, ge=0.0, le=20.0, description="Guidance scale (0.0-20.0)")

    @field_validator('prompt')
    def prompt_must_not_be_empty(cls, v: str):
        if not v or not v.strip():
            raise ValueError('Prompt cannot be empty')
        return v.strip()

    class Config:
        json_schema_extra = {
            "example": {
                "prompt": "a beautiful mountain landscape at sunset",
                "negativePrompt": "blurry, low quality",
                "imageCount": 1,
                "stepCount": 20,
                "seed": 42,
                "guidanceScale": 4.5
            }
        }


class GenerationResponse(BaseModel):
    """Response model for generation request"""
    id: str = Field(..., description="Unique identifier for tracking the generation")
    message: str = Field("Generation started", description="Status message")

    class Config:
        json_schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-************",
                "message": "Generation started"
            }
        }


class GenerationStatus(str, Enum):
    """Generation status enumeration"""
    pending = "pending"
    error = "error"
    complete = "complete"


class ProgressInfo(BaseModel):
    """Progress information for ongoing generation"""
    currentStep: int = Field(..., description="Current step")
    totalSteps: int = Field(..., description="Total steps")

    @property
    def percentage(self) -> int:
        """Progress percentage (0-100)"""
        if self.totalSteps <= 0:
            return 0
        return int((self.currentStep / self.totalSteps) * 100)

    class Config:
        json_schema_extra = {
            "example": {
                "currentStep": 10,
                "totalSteps": 20
            }
        }


class StatusResponse(BaseModel):
    """Response model for status check"""
    status: GenerationStatus = Field(..., description="Generation status")
    error: Optional[str] = Field(None, description="Optional error message if status is error")
    images: Optional[List[str]] = Field(None, description="Generated images as base64 encoded PNG data if status is complete")
    progress: Optional[ProgressInfo] = Field(None, description="Progress information (step/total steps)")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "complete",
                "error": None,
                "images": ["iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="],
                "progress": None
            }
        }


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error message")
    code: int = Field(400, description="HTTP status code")

    class Config:
        json_schema_extra = {
            "example": {
                "error": "Prompt cannot be empty",
                "code": 400
            }
        }


class ModelInfo(BaseModel):
    """Model information response"""
    model_path: str = Field(..., description="Path to the loaded model")
    model_type: str = Field(..., description="Type of model (sdxl or pony)")
    model_name: str = Field(..., description="Name of the model file")
    default_guidance_scale: float = Field(..., description="Default guidance scale for this model")
    supports_quality_tags: bool = Field(..., description="Whether model supports quality score tags")

    class Config:
        json_schema_extra = {
            "example": {
                "model_path": "/path/to/model.safetensors",
                "model_type": "pony",
                "model_name": "pony_diffusion_v6.safetensors",
                "default_guidance_scale": 4.5,
                "supports_quality_tags": True
            }
        }


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str = Field("healthy", description="Service status")
    service: str = Field("image-generation", description="Service name")
    model_info: Optional[ModelInfo] = Field(None, description="Information about the loaded model")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "service": "image-generation",
                "model_info": {
                    "model_type": "pony",
                    "model_name": "pony_diffusion_v6.safetensors",
                    "default_guidance_scale": 4.5,
                    "supports_quality_tags": True
                }
            }
        }
