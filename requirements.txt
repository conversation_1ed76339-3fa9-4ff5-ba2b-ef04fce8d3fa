accelerate==1.7.0
annotated-types==0.7.0
anyio==4.9.0
certifi==2025.4.26
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
contourpy==1.3.2
cycler==0.12.1
# diffusers==0.30.2  # Replaced with k-diffusion
k-diffusion
einops
kornia
torchdiffeq
torchsde
clean-fid
clip-anytorch
dctorch
jsonmerge
scikit-image
fastapi==0.115.12
filelock==3.18.0
fonttools==4.58.2
fsspec==2025.5.1
h11==0.16.0
httptools==0.6.4
huggingface-hub==0.32.5
idna==3.10
importlib_metadata==8.7.0
iniconfig==2.1.0
Jinja2==3.1.6
joblib==1.5.1
kiwisolver==1.4.8
MarkupSafe==3.0.2
matplotlib==3.10.3
mpmath==1.3.0
networkx==3.5
numpy==2.3.0
packaging==25.0
pillow==11.2.1
pluggy==1.6.0
psutil==7.0.0
pydantic==2.11.5
pydantic_core==2.33.2
Pygments==2.19.1
pyparsing==3.2.3
pytest==8.4.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.4
safetensors==0.5.3
scikit-learn==1.7.0
scipy==1.15.3
setuptools==80.9.0
six==1.17.0
sniffio==1.3.1
starlette==0.46.2
sympy==1.14.0
threadpoolctl==3.6.0
tokenizers==0.19.1
torch==2.7.1+cu118
torchaudio==2.7.1+cu118
torchvision==0.22.1+cu118
tqdm==4.67.1
transformers==4.44.2
typing-inspection==0.4.1
typing_extensions==4.14.0
urllib3==2.4.0
uvicorn==0.34.3
watchfiles==1.0.5
websockets==15.0.1
zipp==3.23.0
