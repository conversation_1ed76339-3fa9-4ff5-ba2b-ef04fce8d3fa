#!/bin/bash

# Test script for Image Generation Service
echo "🧪 Testing Image Generation Service"
HOSTNAME="127.0.0.1"
PORT="8074"

# Check if service is running
echo "📡 Testing health endpoint..."
curl -s http://$HOSTNAME:$PORT/health | jq . || echo "❌ Health check failed"

echo ""
echo "🎨 Testing image generation..."

# Test image generation
RESPONSE=$(curl -s -X POST http://$HOSTNAME:$PORT/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a beautiful sunset over mountains",
    "negativePrompt": "blurry, low quality",
    "imageCount": 1,
    "stepCount": 20,
    "seed": 42,
    "guidanceScale": 7.5
  }')

echo "Generation response: $RESPONSE"

# Extract ID from response
ID=$(echo $RESPONSE | jq -r '.id' 2>/dev/null)

if [ "$ID" != "null" ] && [ "$ID" != "" ]; then
    echo "✅ Generation started with ID: $ID"

    echo "📊 Checking status..."
    for i in {1..10}; do
        STATUS_RESPONSE=$(curl -s http://$HOSTNAME:$PORT/status/$ID)
        echo "Status check $i: $STATUS_RESPONSE"

        STATUS=$(echo $STATUS_RESPONSE | jq -r '.status' 2>/dev/null)

        if [ "$STATUS" = "complete" ]; then
            echo "✅ Generation completed!"
            break
        elif [ "$STATUS" = "error" ]; then
            echo "❌ Generation failed!"
            break
        else
            echo "⏳ Generation in progress..."
            sleep 5
        fi
    done

    echo "🧹 Cleaning up..."
    curl -s -X DELETE http://$HOSTNAME:$PORT/generation/$ID
    echo "✅ Cleanup complete"
else
    echo "❌ Failed to start generation"
fi

echo ""
echo "🏁 Test complete"
