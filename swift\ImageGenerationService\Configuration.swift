// For licensing see accompanying LICENSE.md file.
// Copyright (C) 2024 Apple Inc. All Rights Reserved.

import Foundation
import StableDiffusion

/// Configuration for the Image Generation Service
public struct ServiceConfiguration: Codable {
    /// Path to the directory containing Core ML models and tokenization resources
    public let resourcePath: String

    /// The scheduler type to use for image generation
    public let scheduler: String

    /// Host address for the HTTP server (default: "127.0.0.1")
    public let host: String

    /// Port number for the HTTP server (default: 8074)
    public let port: Int

    /// Whether to reduce memory usage (recommended for iOS)
    public let reduceMemory: Bool

    public init(
        resourcePath: String = "./custom_model_coreml/Resources",
        scheduler: String = "pndmScheduler",
        host: String = "127.0.0.1",
        port: Int = 8074,
        reduceMemory: Bool = false
    ) {
        self.resourcePath = resourcePath
        self.scheduler = scheduler
        self.port = port
        self.host = host
        self.reduceMemory = reduceMemory
    }
}

/// Configuration manager for loading service configuration from file
public class ConfigurationManager {

    /// Convert scheduler string to StableDiffusionScheduler enum
    /// - Parameter schedulerString: String representation of scheduler
    /// - Returns: StableDiffusionScheduler enum value
    /// - Throws: Error if scheduler string is not recognized
    public static func parseScheduler(_ schedulerString: String) throws -> StableDiffusionScheduler {
        switch schedulerString.lowercased() {
        case "pndmscheduler", "pndm":
            return .pndmScheduler
        case "dpmsolverMultistepScheduler", "dpmsolver", "dpm":
            return .dpmSolverMultistepScheduler
        case "discreteflowscheduler", "discreteflow", "flow":
            return .discreteFlowScheduler
        default:
            throw ConfigurationError.invalidScheduler(schedulerString)
        }
    }

    /// Validate that the resource path exists and contains required files
    /// - Parameter resourcePath: Path to validate
    /// - Throws: Error if path is invalid or missing required files
    public static func validateResourcePath(_ resourcePath: String) throws {
        let url = URL(fileURLWithPath: resourcePath)

        // Check if directory exists
        var isDirectory: ObjCBool = false
        guard FileManager.default.fileExists(atPath: resourcePath, isDirectory: &isDirectory),
              isDirectory.boolValue else {
            throw ConfigurationError.invalidResourcePath("Directory does not exist: \(resourcePath)")
        }

        // Check for required files
        let requiredFiles = [
            "vocab.json",
            "merges.txt"
        ]

        let requiredModels = [
            "TextEncoder.mlmodelc",
            "Unet.mlmodelc",
            "VAEDecoder.mlmodelc"
        ]

        for file in requiredFiles {
            let filePath = url.appendingPathComponent(file).path
            if !FileManager.default.fileExists(atPath: filePath) {
                throw ConfigurationError.missingRequiredFile(file)
            }
        }

        for model in requiredModels {
            let modelPath = url.appendingPathComponent(model).path
            if !FileManager.default.fileExists(atPath: modelPath) {
                throw ConfigurationError.missingRequiredFile(model)
            }
        }
    }
}

/// Configuration-related errors
public enum ConfigurationError: Error, LocalizedError {
    case invalidScheduler(String)
    case invalidResourcePath(String)
    case missingRequiredFile(String)

    public var errorDescription: String? {
        switch self {
        case .invalidScheduler(let scheduler):
            return "Invalid scheduler: \(scheduler). Valid options are: pndmScheduler, dpmSolverMultistepScheduler, discreteFlowScheduler"
        case .invalidResourcePath(let path):
            return "Invalid resource path: \(path)"
        case .missingRequiredFile(let file):
            return "Missing required file: \(file)"
        }
    }
}
