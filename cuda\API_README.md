# SDXL/Pony Image Generation API Server

A FastAPI-based web server for generating images using Stable Diffusion XL (SDXL) and Pony Diffusion models with PyTorch and CUDA support.

## Features

- **REST API** matching the Swift implementation structure
- **Dual Model Support** for both SDXL and Pony Diffusion models
- **Automatic Model Detection** with optimized settings per model type
- **Async generation** with background processing
- **Progress tracking** during image generation
- **Base64 image encoding** for easy web integration
- **CUDA acceleration** with automatic fallback to CPU
- **Memory management** with automatic cleanup of old generations
- **CORS support** for web frontend integration
- **Interactive documentation** with Swagger UI

## Model Support

### SDXL Models
- Standard Stable Diffusion XL models
- Default guidance scale: 7.5
- Standard prompt processing

### Pony Diffusion Models
- Pony Diffusion V6 XL and compatible models
- Automatic detection based on filename patterns
- Default guidance scale: 4.5 (optimized for Pony models)
- CLIP skip: 2 (automatically applied)
- Enhanced prompt processing with quality score tags
- Automatic addition of `score_9, score_8_up, score_7_up` if not present

## API Endpoints

### POST /generate
Start a new image generation.

**Request Body:**
```json
{
  "prompt": "a beautiful mountain landscape at sunset",
  "negativePrompt": "blurry, low quality",
  "imageCount": 1,
  "stepCount": 20,
  "seed": 42,
  "guidanceScale": 7.5
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "message": "Generation started"
}
```

### GET /generation/{id}
Check generation status and get results.

**Response (Pending):**
```json
{
  "status": "pending",
  "progress": {
    "currentStep": 10,
    "totalSteps": 20
  }
}
```

**Response (Complete):**
```json
{
  "status": "complete",
  "images": ["iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="]
}
```

**Response (Error):**
```json
{
  "status": "error",
  "error": "Error message here"
}
```

### DELETE /generation/{id}
Clear completed generation from memory.

**Response:** 204 No Content

### GET /health
Health check endpoint with model information.

**Response:**
```json
{
  "status": "healthy",
  "service": "image-generation",
  "model_info": {
    "model_path": "/path/to/model.safetensors",
    "model_type": "pony",
    "model_name": "pony_diffusion_v6.safetensors",
    "default_guidance_scale": 4.5,
    "supports_quality_tags": true
  }
}
```

### GET /model
Get detailed information about the loaded model.

**Response:**
```json
{
  "model_path": "/path/to/model.safetensors",
  "model_type": "pony",
  "model_name": "pony_diffusion_v6.safetensors",
  "default_guidance_scale": 4.5,
  "supports_quality_tags": true
}
```

### GET /status
Get server status and device information.

**Response:**
```json
{
  "status": "running",
  "generation_in_progress": false,
  "device_info": {
    "device": "cuda",
    "cuda_available": true,
    "device_count": 1,
    "current_device": 0,
    "device_name": "NVIDIA GeForce RTX 4090",
    "cuda_version": "12.1",
    "memory_allocated_gb": 2.5,
    "memory_reserved_gb": 3.0
  }
}
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Make sure you have an SDXL or Pony Diffusion model file (`.safetensors`) in your directory.

## Usage

### Basic Usage
```bash
python run_server.py
```

### With Custom Options
```bash
python run_server.py --model-path path/to/model.safetensors --host 0.0.0.0 --port 8080 --device cuda
```

### Command Line Options
- `--model-path`: Path to the SDXL or Pony safetensors file (auto-detected if not specified)
- `--host`: Host to bind the server to (default: 127.0.0.1)
- `--port`: Port to bind the server to (default: 8000)
- `--device`: Device to use (auto, cuda, cpu) (default: auto)
- `--reload`: Enable auto-reload for development

### Alternative Usage
You can also run the server using uvicorn directly:
```bash
uvicorn cuda.api_server:app --host 0.0.0.0 --port 8000
```

## API Documentation

Once the server is running, you can access:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Request Validation

The API validates all requests with the following constraints:
- `prompt`: Cannot be empty
- `imageCount`: Must be between 1 and 4
- `stepCount`: Must be between 1 and 100
- `guidanceScale`: Must be between 0.0 and 20.0
- `seed`: Must be >= 0

## Error Handling

The API returns appropriate HTTP status codes:
- `200`: Success
- `202`: Generation started (accepted)
- `204`: Generation cleared (no content)
- `400`: Bad request (validation error)
- `404`: Generation not found
- `409`: Generation already in progress
- `500`: Internal server error

## Memory Management

- Only one generation can run at a time
- Completed generations are stored in memory until cleared
- Old generations are automatically cleaned up after 24 hours
- Use the DELETE endpoint to manually clear completed generations

## CORS Support

The server includes CORS middleware to allow cross-origin requests from web frontends. In production, configure specific origins instead of allowing all origins.

## Development

For development with auto-reload:
```bash
python run_server.py --reload
```

## Performance Notes

- First generation may take longer due to model loading
- CUDA acceleration significantly improves generation speed
- Memory usage depends on model size and generation parameters
- Consider using CPU offloading for large models on limited VRAM
