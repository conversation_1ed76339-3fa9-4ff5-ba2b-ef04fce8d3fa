#!/usr/bin/env python3
"""
CUDA-based image generation package for SDXL and Pony Diffusion models using PyTorch.
"""

__version__ = "1.0.0"
__author__ = "Image Generation Service"
__description__ = "SDXL/Pony Image Generation with FastAPI"

from .models import (
    GenerationRequest,
    GenerationResponse,
    GenerationStatus,
    StatusResponse,
    ProgressInfo,
    ErrorResponse,
    HealthResponse,
    ModelInfo
)

from .image_generator import ImageGenerator, find_safetensors_file
from .generation_manager import GenerationManager, GenerationState

__all__ = [
    "GenerationRequest",
    "GenerationResponse",
    "GenerationStatus",
    "StatusResponse",
    "ProgressInfo",
    "ErrorResponse",
    "HealthResponse",
    "ModelInfo",
    "ImageGenerator",
    "GenerationManager",
    "GenerationState",
    "find_safetensors_file"
]
