// For licensing see accompanying LICENSE.md file.
// Copyright (C) 2024 Apple Inc. All Rights Reserved.

import Foundation
import ArgumentParser
import Hummingbird
import StableDiffusion
import Dispatch

/// Image Generation HTTP Service
/// Provides REST API endpoints for generating images using Stable Diffusion
struct ImageGenerationService: ParsableCommand {
    static let configuration = CommandConfiguration(
        abstract: "HTTP service for image generation using Stable Diffusion",
        version: "1.0.0"
    )

    @Option(
        name: .shortAndLong,
        help: "Port number to run the server on"
    )
    var port: Int?

    @Option(
        name: .shortAndLong,
        help: "Host address to bind the server to"
    )
    var host: String?

    func run() throws {
        Task {
            try await runAsync()
            Foundation.exit(0)
        }
        RunLoop.main.run()
    }

    func runAsync() async throws {
        print("🚀 Starting Image Generation Service...")

        do {
            // Load configuration
            print("📍 Loading configuration...")
            let serviceConfig = try loadConfiguration()
            print("📍 Configuration loaded successfully")

            print("📋 Configuration loaded:")
            print("   Resource Path: \(serviceConfig.resourcePath)")
            print("   Scheduler: \(serviceConfig.scheduler)")
            print("   Host: \(serviceConfig.host)")
            print("   Port: \(serviceConfig.port)")
            print("   Reduce Memory: \(serviceConfig.reduceMemory)")

            // Validate resource path
            print("📍 Validating resource path...")
            try ConfigurationManager.validateResourcePath(serviceConfig.resourcePath)
            print("✅ Resource path validated")

            // Parse scheduler
            print("📍 Parsing scheduler...")
            let scheduler = try ConfigurationManager.parseScheduler(serviceConfig.scheduler)
            print("📍 Scheduler parsed: \(scheduler)")

            // Initialize generation manager
            print("🔧 Initializing Stable Diffusion pipeline...")
            let generationManager = try GenerationManager(
                resourcePath: serviceConfig.resourcePath,
                scheduler: scheduler,
                reduceMemory: serviceConfig.reduceMemory
            )
            print("✅ Pipeline initialized successfully")

            print("📍 Creating Hummingbird application...")
            // Create Hummingbird application
            let router = Router()

            // Add middleware
            print("📍 Adding logging middleware...")
            router.middlewares.add(LoggingMiddleware())
            print("📍 Adding CORS middleware...")
            router.middlewares.add(CORSMiddleware())

            print("📍 Configuring routes...")
            // Configure routes
            let routes = ImageGenerationRoutes(generationManager: generationManager)
            routes.configure(router)

            print("📍 Creating application...")
            let app = Application(
                router: router,
                configuration: .init(
                    address: .hostname(serviceConfig.host, port: serviceConfig.port),
                    serverName: "ImageGenerationService/1.0"
                )
            )

            print("🌐 Server starting on http://\(serviceConfig.host):\(serviceConfig.port)")
            print("📖 API Documentation:")
            print("   POST /generate - Start image generation")
            print("   GET /generation/{id} - Check generation status")
            print("   DELETE /generation/{id} - Clear completed generation")
            print("   GET /health - Health check")
            print("")
            print("💡 Privacy Notice: All prompts and images are stored only in memory")
            print("   and are automatically cleared when the generation is complete.")
            print("")
            print("Press Ctrl-C to stop the server")
            print("")

            // Start the server
            try await app.runService()
            print("✅ Server stopped")

        } catch {
            print("❌ Service failed to start: \(error)")
            Foundation.exit(1)
        }
    }

    /// Load and merge configuration from file and command line arguments
    private func loadConfiguration() throws -> ServiceConfiguration {
        var serviceConfig: ServiceConfiguration

        // Default configuration if file doesn't exist
        serviceConfig = ServiceConfiguration(
            resourcePath: "./custom_model_coreml/Resources",
            scheduler: "pndmScheduler",
            host: "127.0.0.1",
            port: 8074,
            reduceMemory: false
        )

        return serviceConfig
    }
}

/// Error handling for the main function
enum ServiceError: Error, LocalizedError {
    case configurationError(String)
    case initializationError(String)

    var errorDescription: String? {
        switch self {
        case .configurationError(let message):
            return "Configuration error: \(message)"
        case .initializationError(let message):
            return "Initialization error: \(message)"
        }
    }
}

// Manual entry point
ImageGenerationService.main()
