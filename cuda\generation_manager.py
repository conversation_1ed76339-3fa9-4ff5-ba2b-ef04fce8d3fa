#!/usr/bin/env python3
"""
Generation manager for handling image generation requests and state.
This manages the async generation process and tracks generation status.
"""

import asyncio
import uuid
import gc
from typing import Dict, Optional
from datetime import datetime
try:
    # Try relative imports first (when run as module)
    from .models import GenerationRequest, GenerationStatus, StatusResponse, ProgressInfo
    from .image_generator import ImageGenerator
except ImportError:
    # Fall back to absolute imports (when run directly)
    from models import GenerationRequest, GenerationStatus, StatusResponse, ProgressInfo
    from image_generator import ImageGenerator


class GenerationState:
    """In-memory generation state"""
    
    def __init__(self, id: str, request: GenerationRequest):
        self.id = id
        self.request = request
        self.status = GenerationStatus.pending
        self.error: Optional[str] = None
        self.images: Optional[list] = None
        self.progress: Optional[ProgressInfo] = None
        self.created_at = datetime.now()
        self.completed_at: Optional[datetime] = None


class GenerationManager:
    """Manager for handling image generation requests"""
    
    def __init__(self, model_path: str, device: str = "auto"):
        """
        Initialize the generation manager.
        
        Args:
            model_path: Path to the SDXL safetensors file
            device: Device to use ("cuda", "cpu", or "auto")
        """
        self.model_path = model_path
        self.device = device
        self.generator: Optional[ImageGenerator] = None
        self.generations: Dict[str, GenerationState] = {}
        self.current_generation_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """Initialize the image generator (async to avoid blocking startup)"""
        try:
            print("Initializing image generator...")
            # Run the potentially blocking initialization in a thread pool
            loop = asyncio.get_event_loop()
            self.generator = await loop.run_in_executor(
                None, 
                lambda: ImageGenerator(self.model_path, self.device)
            )
            print("✓ Image generator initialized successfully")
        except Exception as e:
            print(f"✗ Failed to initialize image generator: {e}")
            raise
    
    async def start_generation(self, request: GenerationRequest) -> str:
        """
        Start a new image generation.
        
        Args:
            request: Generation request parameters
            
        Returns:
            Unique generation ID
            
        Raises:
            RuntimeError: If generation is already in progress or generator not initialized
        """
        async with self._lock:
            # Check if generator is initialized
            if not self.generator:
                raise RuntimeError("Image generator not initialized")
            
            # Check if generation is already in progress
            if self.current_generation_task and not self.current_generation_task.done():
                raise RuntimeError("A generation is already in progress. Please wait for it to complete.")
            
            # Generate unique ID
            generation_id = str(uuid.uuid4())
            
            # Create new generation state
            state = GenerationState(generation_id, request)
            self.generations[generation_id] = state
            
            # Start generation in background
            self.current_generation_task = asyncio.create_task(
                self._perform_generation(state)
            )
            
            print(f"🎨 Generation started: {generation_id}")
            return generation_id
    
    def get_status(self, generation_id: str) -> StatusResponse:
        """
        Get the status of a generation.
        
        Args:
            generation_id: Generation ID
            
        Returns:
            Status response
            
        Raises:
            KeyError: If generation ID is not found
        """
        if generation_id not in self.generations:
            raise KeyError(f"Generation with ID {generation_id} not found")
        
        state = self.generations[generation_id]
        
        if state.status == GenerationStatus.pending:
            return StatusResponse(
                status=state.status,
                progress=state.progress
            )
        elif state.status == GenerationStatus.error:
            return StatusResponse(
                status=state.status,
                error=state.error
            )
        else:  # complete
            return StatusResponse(
                status=state.status,
                images=state.images
            )
    
    async def clear_generation(self, generation_id: str) -> bool:
        """
        Clear completed or errored generation from memory.

        Args:
            generation_id: Generation ID

        Returns:
            True if generation was cleared, False if not found or still pending
        """
        async with self._lock:
            if generation_id not in self.generations:
                return False

            state = self.generations[generation_id]

            # Only clear if not pending
            if state.status != GenerationStatus.pending:
                # Clear any stored images from memory
                if state.images:
                    state.images.clear()
                    state.images = None

                del self.generations[generation_id]

                # Force garbage collection after clearing large data
                gc.collect()

                print(f"🗑️ Generation cleared: {generation_id}")
                return True

            return False
    
    def is_generation_in_progress(self) -> bool:
        """Check if a generation is currently in progress"""
        return (self.current_generation_task is not None and 
                not self.current_generation_task.done())
    
    def get_device_info(self) -> dict:
        """Get device information from the generator"""
        if self.generator:
            return self.generator.get_device_info()
        return {"device": "unknown", "cuda_available": False}
    
    async def _perform_generation(self, state: GenerationState):
        """
        Perform the actual image generation in the background.
        
        Args:
            state: Generation state to update
        """
        try:
            print(f"📊 Starting generation: {state.id}")
            
            # Progress callback to update state
            def progress_callback(current_step: int, total_steps: int):
                state.progress = ProgressInfo(
                    currentStep=current_step,
                    totalSteps=total_steps
                )
                #print(f"📊 Generation {state.id}: step {current_step}/{total_steps}")
            
            # Run generation in thread pool to avoid blocking the event loop
            loop = asyncio.get_event_loop()
            images = await loop.run_in_executor(
                None,
                lambda: self.generator.generate_images(
                    prompt=state.request.prompt,
                    negative_prompt=state.request.negativePrompt,
                    image_count=state.request.imageCount,
                    step_count=state.request.stepCount,
                    guidance_scale=state.request.guidanceScale,
                    seed=state.request.seed,
                    progress_callback=progress_callback
                )
            )
            
            # Update state with results
            state.status = GenerationStatus.complete
            state.images = images
            state.progress = None
            state.completed_at = datetime.now()
            
            print(f"✅ Generation completed: {state.id}")
            
        except Exception as e:
            # Update state with error
            state.status = GenerationStatus.error
            state.error = str(e)
            state.progress = None
            state.completed_at = datetime.now()
            
            print(f"❌ Generation failed: {state.id} - {e}")
    
    async def cleanup_old_generations(self, max_age_hours: int = 24):
        """
        Clean up old completed/errored generations.

        Args:
            max_age_hours: Maximum age in hours before cleanup
        """
        async with self._lock:
            current_time = datetime.now()
            to_remove = []

            for gen_id, state in self.generations.items():
                if (state.status != GenerationStatus.pending and
                    state.completed_at and
                    (current_time - state.completed_at).total_seconds() > max_age_hours * 3600):
                    to_remove.append(gen_id)

            for gen_id in to_remove:
                state = self.generations[gen_id]
                # Clear any stored images from memory before deletion
                if state.images:
                    state.images.clear()
                    state.images = None
                del self.generations[gen_id]
                print(f"🧹 Cleaned up old generation: {gen_id}")

            if to_remove:
                # Force garbage collection after cleanup
                gc.collect()
                print(f"🧹 Cleaned up {len(to_remove)} old generations")

    async def cleanup(self):
        """Clean up the generation manager and free resources"""
        async with self._lock:
            try:
                # Cancel any running generation task
                if self.current_generation_task and not self.current_generation_task.done():
                    self.current_generation_task.cancel()
                    try:
                        await self.current_generation_task
                    except asyncio.CancelledError:
                        pass

                # Clear all generations
                for state in self.generations.values():
                    if state.images:
                        state.images.clear()
                        state.images = None

                self.generations.clear()

                # Clean up the image generator
                if self.generator:
                    self.generator.cleanup()
                    self.generator = None

                # Force garbage collection
                gc.collect()

                print("✓ GenerationManager cleanup completed")

            except Exception as e:
                print(f"Warning: Error during GenerationManager cleanup: {e}")
