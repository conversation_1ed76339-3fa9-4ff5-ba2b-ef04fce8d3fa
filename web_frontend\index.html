
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Generation Client</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            background-color: #222;
        }
        .container {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }
        .sidebar {
            width: 350px;
            background-color: #111;
            border-right: 1px solid #555;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        h2 {
            color: #666;
            margin-bottom: 20px;
            font-size: 1.2em;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 8px;
            background-color: #111;
            color: #aaa;
            border: 1px solid #333;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #cc6600;
            border-color: #111;
            color: #111;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #bb5500;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .queue-item {
            border: 1px solid #333;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #222;
        }
        .queue-item.active {
            border-color: #0066cc;
        }
        .queue-item.completed {
            border-color: #28a745;
        }
        .queue-item.error {
            border-color: #dc3545;
        }
        .queue-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .queue-item-status {
            display: none;
            font-weight: bold;
            font-size: 0.9em;
            padding: 4px 8px;
            border-radius: 4px;
            text-transform: uppercase;
        }
        .status-pending {
            background-color: #ffc107;
            color: #000;
        }
        .status-active {
            background-color: #0066cc;
            color: #fff;
        }
        .status-completed {
            background-color: #28a745;
            color: #fff;
        }
        .status-error {
            background-color: #dc3545;
            color: #fff;
        }
        .queue-item-prompt {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 10px;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .queue-item-actions {
            display: flex;
            gap: 10px;
        }
        .cancel-btn {
            background-color: #dc3545;
            font-size: 14px;
            padding: 6px 12px;
        }
        .cancel-btn:hover {
            background-color: #c82333;
        }
        .remove-btn {
            background-color: #6c757d;
            font-size: 14px;
            padding: 6px 12px;
        }
        .remove-btn:hover {
            background-color: #5a6268;
        }
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            border: 1px solid #333;
            border-radius: 8px;
            padding: 15px;
        }
        .image-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        .save-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .save-btn:hover {
            background-color: #218838;
        }
        .remove-image-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .remove-image-btn:hover {
            background-color: #c82333;
        }
        .progress-bar {
            height: 20px;
            background-color: #e9ecef;
            border-radius: 4px;
            margin-top: 10px;
        }
        .progress-bar-fill {
            height: 100%;
            background-color: #0066cc;
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s ease;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            cursor: pointer;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 95%;
            max-height: 95%;
            object-fit: contain;
        }
        .modal-close {
            position: absolute;
            top: 20px;
            right: 35px;
            color: #fff;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            z-index: 1001;
        }
        .modal-close:hover {
            color: #ccc;
        }
        .modal-controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1001;
            display: flex;
            gap: 10px;
        }
        .modal-btn {
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            backdrop-filter: blur(5px);
        }
        .modal-btn:hover {
            background-color: rgba(0, 0, 0, 0.9);
        }
        .modal-content.actual-size {
            max-width: none;
            max-height: none;
            width: auto;
            height: auto;
        }
        .image-item img {
            width: 100%;
            height: auto;
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: opacity 0.2s ease;
        }
        .image-item img:hover {
            opacity: 0.8;
        }
        .form-container {
            background-color: #333;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .queue-empty {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .form-row .form-group label {
            margin-bottom: 0;
            font-size: 0.85em;
            white-space: nowrap;
            min-width: fit-content;
        }
        .form-row .form-group input {
            flex: 1;
            min-width: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar for queue management -->
        <div class="sidebar">
            <h2>Generation Queue</h2>
            <div id="queueContainer">
                <div class="queue-empty" id="queueEmpty">
                    No requests in queue
                </div>
            </div>
        </div>

        <!-- Main content area -->
        <div class="main-content">
            <!-- Generation form -->
            <div class="form-container">
                <form id="generationForm">
                    <div class="form-group">
                        <label for="prompt">Prompt:</label>
                        <textarea id="prompt" name="prompt" rows="2" required>a beautiful sunset over mountains</textarea>
                    </div>

                    <div class="form-group">
                        <label for="negativePrompt">Negative Prompt:</label>
                        <textarea id="negativePrompt" name="negativePrompt" rows="1">blurry, low quality, censored</textarea>
                    </div>

                    <!-- Disabled until The Issue is resolved
                    <div class="form-group">
                        <label for="imageCount">Image Count (1-4):</label>
                        <input type="number" id="imageCount" name="imageCount" min="1" max="4" value="1" required>
                    </div> -->

                    <div class="form-row">
                        <div class="form-group">
                            <label for="stepCount">Step Count (1-100):</label>
                            <input type="number" id="stepCount" name="stepCount" min="1" max="100" value="35" required>
                        </div>

                        <div class="form-group">
                            <label for="seed">Seed (0 for random):</label>
                            <input type="number" id="seed" name="seed" min="0" value="0" required>
                        </div>

                        <div class="form-group">
                            <label for="guidanceScale">Guidance Scale (0.0-20.0):</label>
                            <input type="number" id="guidanceScale" name="guidanceScale" min="0" max="20" step="0.1" value="4.5" required>
                        </div>
                    </div>

                    <button type="submit" id="generateBtn">Add to Queue</button>
                </form>
            </div>

            <!-- Generated images gallery -->
            <div class="image-container" id="imageContainer">
                <h2 id="imageTitle">Generated Images</h2>
                <div class="images-grid" id="imagesGrid">
                    <!-- Generated images will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for full-size image viewing -->
    <div id="imageModal" class="modal">
        <div class="modal-controls">
            <button class="modal-btn" id="toggleSizeBtn">Actual Size</button>
        </div>
        <span class="modal-close" id="modalClose">&times;</span>
        <img class="modal-content" id="modalImage" alt="Full size image">
    </div>

    <script>
        const API_URL = ''; // APIs are relative to the current page

        // Queue management
        let requestQueue = [];
        let currentRequest = null;
        let statusCheckInterval = null;
        let nextRequestId = 1;

        // Queue item statuses
        const QueueStatus = {
            PENDING: 'pending',
            ACTIVE: 'active',
            COMPLETED: 'completed',
            ERROR: 'error'
        };

        document.getElementById('generationForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Prepare request data
            const requestData = {
                prompt: document.getElementById('prompt').value,
                negativePrompt: document.getElementById('negativePrompt').value,
                imageCount: 1, //  Disabled until The Issue is resolved  // parseInt(document.getElementById('imageCount').value),
                stepCount: parseInt(document.getElementById('stepCount').value),
                seed: parseInt(document.getElementById('seed').value),
                guidanceScale: parseFloat(document.getElementById('guidanceScale').value)
            };

            // Create queue item
            const queueItem = {
                id: nextRequestId++,
                status: QueueStatus.PENDING,
                requestData: requestData,
                generationId: null,
                images: null,
                error: null,
                progress: null,
                timestamp: new Date()
            };

            // Add to queue
            requestQueue.push(queueItem);
            updateQueueDisplay();

            // Process queue if not already processing
            if (!currentRequest) {
                processQueue();
            }
        });

        async function processQueue() {
            // Find next pending request
            const nextRequest = requestQueue.find(item => item.status === QueueStatus.PENDING);
            if (!nextRequest) {
                currentRequest = null;
                return;
            }

            currentRequest = nextRequest;
            nextRequest.status = QueueStatus.ACTIVE;
            updateQueueDisplay();

            try {
                // Send generation request
                const response = await fetch(`${API_URL}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(nextRequest.requestData)
                });

                if (!response.ok) {
                    throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                nextRequest.generationId = data.id;

                // Start checking status
                checkStatus(nextRequest);
                statusCheckInterval = setInterval(() => checkStatus(nextRequest), 2000);

            } catch (error) {
                nextRequest.status = QueueStatus.ERROR;
                nextRequest.error = error.message;
                updateQueueDisplay();

                // Process next request
                currentRequest = null;
                processQueue();
            }
        }

        async function checkStatus(queueItem) {
            if (!queueItem.generationId) return;

            try {
                const response = await fetch(`${API_URL}/generation/${queueItem.generationId}`);
                if (!response.ok) {
                    throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.status === 'pending' && data.progress) {
                    queueItem.progress = data.progress;
                    updateQueueDisplay();
                } else if (data.status === 'complete') {
                    clearInterval(statusCheckInterval);

                    // Display images before removing from queue
                    if (data.images && data.images.length > 0) {
                        displayImages(data.images, queueItem);
                    }

                    // Clean up the generation on the server
                    cleanupGeneration(queueItem.generationId);

                    // Remove completed item from queue
                    const itemIndex = requestQueue.findIndex(item => item.id === queueItem.id);
                    if (itemIndex !== -1) {
                        requestQueue.splice(itemIndex, 1);
                    }

                    // Remove the DOM element
                    const queueItemElement = document.getElementById(`queue-item-${queueItem.id}`);
                    if (queueItemElement) {
                        queueItemElement.remove();
                    }

                    updateQueueDisplay();

                    // Process next request
                    currentRequest = null;
                    processQueue();
                } else if (data.status === 'error') {
                    clearInterval(statusCheckInterval);
                    queueItem.status = QueueStatus.ERROR;
                    queueItem.error = data.error || 'Unknown error occurred';

                    // Clean up the generation on the server
                    cleanupGeneration(queueItem.generationId);

                    updateQueueDisplay();

                    // Process next request
                    currentRequest = null;
                    processQueue();
                }
            } catch (error) {
                queueItem.status = QueueStatus.ERROR;
                queueItem.error = `Error checking status: ${error.message}`;
                updateQueueDisplay();

                // Process next request
                currentRequest = null;
                processQueue();
            }
        }

        async function cleanupGeneration(generationId) {
            if (!generationId) return;

            try {
                await fetch(`${API_URL}/generation/${generationId}`, {
                    method: 'DELETE'
                });
                console.log(`Cleaned up generation ${generationId}`);
            } catch (error) {
                console.error(`Error cleaning up generation: ${error.message}`);
            }
        }

        function updateQueueDisplay() {
            const queueContainer = document.getElementById('queueContainer');
            const queueEmpty = document.getElementById('queueEmpty');

            if (requestQueue.length === 0) {
                queueEmpty.style.display = 'block';
                // Remove all queue items
                const queueItems = queueContainer.querySelectorAll('.queue-item');
                queueItems.forEach(item => item.remove());
                return;
            }

            queueEmpty.style.display = 'none';

            // Update existing items and add new ones
            requestQueue.forEach(queueItem => {
                let queueItemElement = document.getElementById(`queue-item-${queueItem.id}`);

                if (!queueItemElement) {
                    // Create new queue item
                    queueItemElement = createQueueItemElement(queueItem);
                    queueContainer.appendChild(queueItemElement);
                } else {
                    // Update existing queue item
                    updateQueueItemElement(queueItemElement, queueItem);
                }
            });
        }

        function createQueueItemElement(queueItem) {
            const element = document.createElement('div');
            element.className = `queue-item ${queueItem.status}`;
            element.id = `queue-item-${queueItem.id}`;

            updateQueueItemElement(element, queueItem);
            return element;
        }

        function updateQueueItemElement(element, queueItem) {
            element.className = `queue-item ${queueItem.status}`;

            const statusText = {
                [QueueStatus.PENDING]: 'Pending',
                [QueueStatus.ACTIVE]: 'Processing',
                [QueueStatus.COMPLETED]: 'Completed',
                [QueueStatus.ERROR]: 'Error'
            };

            let progressHtml = '';
            if (queueItem.status === QueueStatus.ACTIVE && queueItem.progress) {
                const percentage = (queueItem.progress.currentStep / queueItem.progress.totalSteps * 100).toFixed(0);
                progressHtml = `
                    <div class="progress-bar">
                        <div class="progress-bar-fill" style="width: ${percentage}%"></div>
                    </div>
                    <div style="font-size: 0.8em; margin-top: 5px;">
                        Step ${queueItem.progress.currentStep}/${queueItem.progress.totalSteps} (${percentage}%)
                    </div>
                `;
            }

            let actionsHtml = '';
            if (queueItem.status === QueueStatus.PENDING) {
                actionsHtml = `
                    <div class="queue-item-actions">
                        <button class="cancel-btn" onclick="cancelQueueItem(${queueItem.id})">Cancel</button>
                    </div>
                `;
            } else if (queueItem.status === QueueStatus.ERROR) {
                actionsHtml = `
                    <div class="queue-item-actions">
                        <button class="remove-btn" onclick="removeQueueItem(${queueItem.id})">Remove</button>
                    </div>
                `;
            }

            let errorHtml = '';
            if (queueItem.status === QueueStatus.ERROR && queueItem.error) {
                errorHtml = `<div style="color: #dc3545; font-size: 0.8em; margin-top: 5px;">${queueItem.error}</div>`;
            }

            element.innerHTML = `
                <div class="queue-item-header">
                    <span class="queue-item-status status-${queueItem.status}">${statusText[queueItem.status]}</span>
                    <span style="font-size: 0.8em; color: #666;">#${queueItem.id}</span>
                </div>
                <div class="queue-item-prompt">${queueItem.requestData.prompt}</div>
                ${progressHtml}
                ${errorHtml}
                ${actionsHtml}
            `;
        }

        function cancelQueueItem(itemId) {
            const itemIndex = requestQueue.findIndex(item => item.id === itemId);
            if (itemIndex !== -1 && requestQueue[itemIndex].status === QueueStatus.PENDING) {
                // Remove from queue array
                requestQueue.splice(itemIndex, 1);

                // Remove the DOM element
                const queueItemElement = document.getElementById(`queue-item-${itemId}`);
                if (queueItemElement) {
                    queueItemElement.remove();
                }

                updateQueueDisplay();
            }
        }

        function removeQueueItem(itemId) {
            const itemIndex = requestQueue.findIndex(item => item.id === itemId);
            if (itemIndex !== -1) {
                // Remove from queue array
                requestQueue.splice(itemIndex, 1);

                // Remove the DOM element
                const queueItemElement = document.getElementById(`queue-item-${itemId}`);
                if (queueItemElement) {
                    queueItemElement.remove();
                }

                // Update display
                updateQueueDisplay();
            }
        }

        function displayImages(images, queueItem) {
            const imageContainer = document.getElementById('imageContainer');
            const imagesGrid = document.getElementById('imagesGrid');
            const imageTitle = document.getElementById('imageTitle');

            // Show image container
            imageContainer.style.display = 'block';

            // Update title
            updateImageTitle();

            // Create image elements for each generated image
            images.forEach((imageData, index) => {
                const imageId = `image-${queueItem.id}-${index}`;
                const imageItem = document.createElement('div');
                imageItem.className = 'image-item';
                imageItem.id = imageId;

                // Add prompt info
                const promptInfo = document.createElement('div');
                promptInfo.style.cssText = 'font-size: 0.9em; color: #666; margin-bottom: 10px; font-weight: bold;';
                promptInfo.textContent = `Request #${queueItem.id}: ${queueItem.requestData.prompt}`;

                const img = document.createElement('img');
                img.src = `data:image/png;base64,${imageData}`;
                img.alt = `Generated image from request ${queueItem.id}`;
                img.onclick = () => openModal(img.src, img.alt);

                const actions = document.createElement('div');
                actions.className = 'image-actions';

                const saveBtn = document.createElement('button');
                saveBtn.className = 'save-btn';
                saveBtn.textContent = 'Save Image';
                saveBtn.onclick = (event) => saveImage(imageData, queueItem.id, event.target);

                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-image-btn';
                removeBtn.textContent = 'Remove';
                removeBtn.onclick = () => removeImage(imageId);

                actions.appendChild(saveBtn);
                actions.appendChild(removeBtn);
                imageItem.appendChild(promptInfo);
                imageItem.appendChild(img);
                imageItem.appendChild(actions);
                imagesGrid.insertBefore(imageItem, imagesGrid.firstChild);
            });
        }

        function removeImage(imageId) {
            const imageElement = document.getElementById(imageId);
            if (imageElement) {
                imageElement.remove();
                updateImageTitle();
            }
        }

        function updateImageTitle() {
            const imageTitle = document.getElementById('imageTitle');
            const imagesGrid = document.getElementById('imagesGrid');
            const imageCount = imagesGrid.children.length;

            if (imageCount === 0) {
                imageTitle.textContent = 'Generated Images';
            } else {
                imageTitle.textContent = `Generated Images (${imageCount} displayed)`;
            }
        }

        function saveImage(base64Data, requestId, button) {
            try {
                // Show loading state
                if (button) {
                    button.textContent = 'Saving...';
                    button.disabled = true;
                }

                // Convert base64 to blob
                const byteCharacters = atob(base64Data);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], { type: 'image/png' });

                // Create download link
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;

                // Generate filename with timestamp
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                link.download = `generated-image-request-${requestId}-${timestamp}.png`;

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up the URL object
                URL.revokeObjectURL(url);

                console.log(`Image from request ${requestId} saved successfully`);

                // Show success state briefly
                if (button) {
                    button.textContent = 'Saved!';
                    setTimeout(() => {
                        button.textContent = 'Save Image';
                        button.disabled = false;
                    }, 1000);
                }
            } catch (error) {
                console.error('Error saving image:', error);
                alert('Failed to save image. Please try again.');

                // Reset button state
                if (button) {
                    button.textContent = 'Save Image';
                    button.disabled = false;
                }
            }
        }

        // Modal functionality
        function openModal(imageSrc, imageAlt) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            const toggleBtn = document.getElementById('toggleSizeBtn');

            modal.style.display = 'block';
            modalImg.src = imageSrc;
            modalImg.alt = imageAlt;

            // Reset to fit-to-screen mode
            modalImg.classList.remove('actual-size');
            toggleBtn.textContent = 'Actual Size';
        }

        function closeModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
        }

        function toggleImageSize() {
            const modalImg = document.getElementById('modalImage');
            const toggleBtn = document.getElementById('toggleSizeBtn');

            if (modalImg.classList.contains('actual-size')) {
                // Switch to fit-to-screen
                modalImg.classList.remove('actual-size');
                toggleBtn.textContent = 'Actual Size';
            } else {
                // Switch to actual size
                modalImg.classList.add('actual-size');
                toggleBtn.textContent = 'Fit to Screen';
            }
        }

        // Event listeners for modal
        document.getElementById('modalClose').onclick = closeModal;
        document.getElementById('toggleSizeBtn').onclick = toggleImageSize;
        document.getElementById('imageModal').onclick = function(event) {
            // Close modal when clicking on the background (not the image or controls)
            if (event.target === this) {
                closeModal();
            }
        };

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize queue display
            updateQueueDisplay();
        });
    </script>
</body>
</html>

