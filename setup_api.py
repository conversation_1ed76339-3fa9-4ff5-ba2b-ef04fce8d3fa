#!/usr/bin/env python3
"""
Setup script to install dependencies and test the API server.
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and print the result"""
    print(f"\n{description}...")
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✓ Success!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def main():
    print("=== SDXL API Server Setup ===")
    
    # Check if we're in the right directory
    if not os.path.exists("cuda/requirements.txt"):
        print("❌ Error: cuda/requirements.txt not found. Please run this script from the project root.")
        sys.exit(1)
    
    # Install dependencies
    if not run_command("pip install -r cuda/requirements.txt", "Installing dependencies"):
        print("❌ Failed to install dependencies. Please check the error messages above.")
        sys.exit(1)
    
    # Test imports
    if not run_command("python test_api_import.py", "Testing imports"):
        print("❌ Import test failed. Please check the error messages above.")
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Make sure you have a .safetensors model file in the current directory")
    print("2. Start the server with: python cuda/run_server.py")
    print("3. Open http://localhost:8000/docs to see the API documentation")
    print("4. Test the API with: curl http://localhost:8000/health")

if __name__ == "__main__":
    main()
