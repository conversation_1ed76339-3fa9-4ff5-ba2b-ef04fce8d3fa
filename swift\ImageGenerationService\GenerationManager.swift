// For licensing see accompanying LICENSE.md file.
// Copyright (C) 2024 Apple Inc. All Rights Reserved.

import Foundation
import StableDiffusion
import CoreML

/// Manager for handling image generation requests
public actor GenerationManager {

    private let pipeline: StableDiffusionPipelineProtocol
    private let scheduler: StableDiffusionScheduler

    // In-memory storage for current generation (privacy protection)
    private var currentGeneration: GenerationState?

    // Task for current generation (for cancellation)
    private var currentGenerationTask: Task<Void, Never>?

    // Shared state holder that can be accessed from both isolated and non-isolated contexts
    private class SharedState {
        var generationState: GenerationState?
    }

    // Make sharedState nonisolated
    private nonisolated let sharedState = SharedState()

    /// Initialize the generation manager
    /// - Parameters:
    ///   - resourcePath: Path to Core ML models and tokenization resources
    ///   - scheduler: Scheduler type to use
    ///   - reduceMemory: Whether to use reduced memory mode
    public init(resourcePath: String, scheduler: StableDiffusionScheduler, reduceMemory: Bool = false) throws {
        let resourceURL = URL(fileURLWithPath: resourcePath)

        // Create ML configuration
        let config = MLModelConfiguration()
        config.computeUnits = .all

        // Initialize the SDXL pipeline
        if #available(macOS 14.0, iOS 17.0, *) {
            self.pipeline = try StableDiffusionXLPipeline(
                resourcesAt: resourceURL,
                configuration: config,
                reduceMemory: reduceMemory
            )
        } else {
            throw GenerationError.unsupportedPlatform("SDXL requires macOS 14.0+ or iOS 17.0+")
        }

        self.scheduler = scheduler

        // Load resources
        try self.pipeline.loadResources()
    }

    /// Start a new image generation
    /// - Parameter request: Generation request parameters
    /// - Returns: Unique generation ID
    /// - Throws: Error if generation is already in progress or request is invalid
    public func startGeneration(_ request: GenerationRequest) throws -> String {
        // Validate request
        try request.validate()

        // Check if generation is already in progress
        if let current = currentGeneration, current.status == .pending {
            throw GenerationError.generationInProgress
        }

        // Generate unique ID
        let id = UUID().uuidString

        // Create new generation state
        let state = GenerationState(id: id, request: request)
        currentGeneration = state
        sharedState.generationState = state

        // Start generation in background
        currentGenerationTask = Task {
            await performGeneration(state)
        }

        return id
    }

    /// Get the status of a generation
    /// - Parameter id: Generation ID
    /// - Returns: Status response
    /// - Throws: Error if generation ID is not found
    public func getStatus(for id: String) throws -> StatusResponse {
        guard let generation = currentGeneration, generation.id == id else {
            throw GenerationError.generationNotFound(id)
        }

        switch generation.status {
        case .pending:
            return StatusResponse(
                status: .pending,
                progress: generation.progress
            )
        case .error:
            return StatusResponse(
                status: .error,
                error: generation.error
            )
        case .complete:
            return StatusResponse(
                status: .complete,
                images: generation.getBase64Images()
            )
        }
    }

    /// Get the status of a generation without blocking on ongoing generation
    /// - Parameter id: Generation ID
    /// - Returns: Status response
    /// - Throws: Error if generation ID is not found
    public nonisolated func getNonBlockingStatus(for id: String) throws -> StatusResponse {
        guard let generation = sharedState.generationState, generation.id == id else {
            throw GenerationError.generationNotFound(id)
        }

        switch generation.status {
        case .pending:
            return StatusResponse(
                status: .pending,
                progress: generation.progress
            )
        case .error:
            return StatusResponse(
                status: .error,
                error: generation.error
            )
        case .complete:
            return StatusResponse(
                status: .complete,
                images: generation.getBase64Images()
            )
        }
    }

    /// Check if a generation is currently in progress
    public var isGenerationInProgress: Bool {
        return currentGeneration?.status == .pending
    }

    /// Clear completed or errored generation from memory (privacy protection)
    public func clearGeneration(id: String) {
        if let generation = currentGeneration,
           generation.id == id,
           generation.status != .pending {
            currentGeneration = nil
            sharedState.generationState = nil
            currentGenerationTask = nil
        }
    }

    /// Perform the actual image generation
    private func performGeneration(_ state: GenerationState) async {
        do {
            // Check for cancellation before starting
            if Task.isCancelled {
                print("🚫 Generation \(state.id): Cancelled before starting")
                updateGenerationError(state: state, error: "Generation cancelled")
                return
            }
            // Create pipeline configuration
            var config = PipelineConfiguration(prompt: state.request.prompt)
            config.negativePrompt = state.request.negativePrompt
            config.imageCount = state.request.imageCount
            config.stepCount = state.request.stepCount
            config.seed = state.request.seed
            config.guidanceScale = state.request.guidanceScale
            config.schedulerType = scheduler

            // SDXL-specific scale factors
            config.encoderScaleFactor = 0.13025
            config.decoderScaleFactor = 0.13025

            // Progress handler to update state and log progress
            let progressHandler: (PipelineProgress) -> Bool = { [self] progress in
                // Check for cancellation
                if Task.isCancelled {
                    print("🚫 Generation \(state.id): Cancelled during step \(progress.step)")
                    return false // Stop generation
                }

                // Update progress synchronously to ensure immediate visibility
                // Since GenerationState is a class, we can update its properties directly
                state.progress = ProgressInfo(currentStep: progress.step, totalSteps: progress.stepCount)

                // Update the shared state
                self.sharedState.generationState = state

                // Log progress every 10%
                print("📊 Generation \(state.id) in progress: step \(progress.step) of \(progress.stepCount)")

                return true
            }

            // Generate images
            let images = try pipeline.generateImages(
                configuration: config,
                progressHandler: progressHandler
            )

            // Filter out nil images (safety check failures)
            let validImages = images.compactMap { $0 }

            if validImages.isEmpty {
                let errorMessage = "All generated images failed safety checks"
                print("❌ Generation \(state.id): Failed - \(errorMessage)")
                updateGenerationError(state: state, error: errorMessage)
            } else {
                print("✅ Generation \(state.id): Complete")
                updateGenerationComplete(state: state, images: validImages)
            }

        } catch {
            if Task.isCancelled {
                print("🚫 Generation \(state.id): Cancelled")
                updateGenerationError(state: state, error: "Generation cancelled")
            } else {
                print("❌ Generation \(state.id): Failed - \(error.localizedDescription)")
                updateGenerationError(state: state, error: error.localizedDescription)
            }
        }

        // Clear the task reference when generation completes
        currentGenerationTask = nil
    }

    /// Update generation with error
    private func updateGenerationError(state: GenerationState, error: String) {
        state.status = .error
        state.error = error
        state.progress = nil
        sharedState.generationState = state
    }

    /// Update generation as complete
    private func updateGenerationComplete(state: GenerationState, images: [CGImage]) {
        state.status = .complete
        state.images = images
        state.progress = nil
        sharedState.generationState = state
    }
}

/// Generation-related errors
public enum GenerationError: Error, LocalizedError {
    case generationInProgress
    case generationNotFound(String)
    case pipelineInitializationFailed(String)
    case unsupportedPlatform(String)

    public var errorDescription: String? {
        switch self {
        case .generationInProgress:
            return "A generation is already in progress. Please wait for it to complete before starting a new one."
        case .generationNotFound(let id):
            return "Generation with ID \(id) not found"
        case .pipelineInitializationFailed(let error):
            return "Failed to initialize pipeline: \(error)"
        case .unsupportedPlatform(let message):
            return "Unsupported platform: \(message)"
        }
    }
}
