import torch
from diffusers import StableDiffusionXLPipeline
import argparse

def convert_safetensors_to_diffusers(checkpoint_path, dump_path):
    """
    Loads an SDXL checkpoint from a single .safetensors file and saves it
    in the diffusers directory format.
    """
    print(f"Loading checkpoint from: {checkpoint_path}")

    # Load the pipeline from a single file.
    # For SDXL, it's crucial to use StableDiffusionXLPipeline.
    pipe = StableDiffusionXLPipeline.from_single_file(
        checkpoint_path,
        torch_dtype=torch.float16,  # Use float16 for better performance on Apple Silicon
        use_safetensors=True
    )

    print(f"Successfully loaded pipeline. Now saving to: {dump_path}")

    # Save the pipeline to the specified directory in diffusers format.
    pipe.save_pretrained(dump_path)

    print("Conversion complete.")
    print(f"Your model is now ready in the '{dump_path}' directory.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert a .safetensors SDXL checkpoint to diffusers format.")
    parser.add_argument("input", type=str, help="Path to the input .safetensors file.")
    parser.add_argument("output", type=str, help="Path to the output directory for the diffusers model.")

    args = parser.parse_args()

    convert_safetensors_to_diffusers(args.input, args.output)