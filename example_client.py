#!/usr/bin/env python3
"""
Example client for the SDXL Image Generation API.
Demonstrates how to use the API endpoints.
"""

import requests
import time
import base64
import json
from pathlib import Path


class ImageGenerationClient:
    """Simple client for the image generation API"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
    
    def health_check(self):
        """Check if the API server is healthy"""
        try:
            response = requests.get(f"{self.base_url}/health")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Health check failed: {e}")
            return None
    
    def get_server_status(self):
        """Get server status and device information"""
        try:
            response = requests.get(f"{self.base_url}/status")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Status check failed: {e}")
            return None
    
    def start_generation(self, prompt: str, **kwargs):
        """Start a new image generation"""
        request_data = {
            "prompt": prompt,
            "negativePrompt": kwargs.get("negative_prompt", ""),
            "imageCount": kwargs.get("image_count", 1),
            "stepCount": kwargs.get("step_count", 20),
            "seed": kwargs.get("seed", 0),
            "guidanceScale": kwargs.get("guidance_scale", 7.5)
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/generate",
                json=request_data,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Generation request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.text}")
            return None
    
    def get_generation_status(self, generation_id: str):
        """Get the status of a generation"""
        try:
            response = requests.get(f"{self.base_url}/generation/{generation_id}")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Status request failed: {e}")
            return None
    
    def clear_generation(self, generation_id: str):
        """Clear a completed generation"""
        try:
            response = requests.delete(f"{self.base_url}/generation/{generation_id}")
            response.raise_for_status()
            return True
        except requests.RequestException as e:
            print(f"Clear request failed: {e}")
            return False
    
    def wait_for_completion(self, generation_id: str, timeout: int = 300):
        """Wait for a generation to complete"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_generation_status(generation_id)
            if not status:
                return None
            
            if status["status"] == "complete":
                return status
            elif status["status"] == "error":
                print(f"Generation failed: {status.get('error', 'Unknown error')}")
                return status
            elif status["status"] == "pending":
                progress = status.get("progress")
                if progress:
                    current = progress["currentStep"]
                    total = progress["totalSteps"]
                    percentage = int((current / total) * 100) if total > 0 else 0
                    print(f"Progress: {current}/{total} ({percentage}%)")
                else:
                    print("Generation pending...")
            
            time.sleep(2)  # Wait 2 seconds before checking again
        
        print("Timeout waiting for generation to complete")
        return None
    
    def save_images(self, images: list, output_dir: str = "output"):
        """Save base64 encoded images to files"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        saved_files = []
        for i, image_b64 in enumerate(images):
            try:
                # Decode base64 image
                image_data = base64.b64decode(image_b64)
                
                # Save to file
                filename = f"generated_image_{int(time.time())}_{i+1}.png"
                file_path = output_path / filename
                
                with open(file_path, "wb") as f:
                    f.write(image_data)
                
                saved_files.append(str(file_path))
                print(f"✓ Saved image: {file_path}")
                
            except Exception as e:
                print(f"❌ Failed to save image {i+1}: {e}")
        
        return saved_files


def main():
    """Example usage of the image generation API"""
    print("=== SDXL Image Generation API Client Example ===")
    
    # Create client
    client = ImageGenerationClient()
    
    # Check health
    print("\n1. Checking API health...")
    health = client.health_check()
    if not health:
        print("❌ API server is not responding. Make sure it's running on http://localhost:8000")
        return
    print(f"✓ API is healthy: {health}")
    
    # Get server status
    print("\n2. Getting server status...")
    status = client.get_server_status()
    if status:
        print(f"✓ Server status: {json.dumps(status, indent=2)}")
    
    # Start generation
    print("\n3. Starting image generation...")
    prompt = "a beautiful mountain landscape at sunset, highly detailed, photorealistic"
    
    generation = client.start_generation(
        prompt=prompt,
        negative_prompt="blurry, low quality, distorted",
        image_count=1,
        step_count=20,
        seed=42,
        guidance_scale=7.5
    )
    
    if not generation:
        print("❌ Failed to start generation")
        return
    
    generation_id = generation["id"]
    print(f"✓ Generation started with ID: {generation_id}")
    
    # Wait for completion
    print("\n4. Waiting for generation to complete...")
    result = client.wait_for_completion(generation_id)
    
    if not result:
        print("❌ Generation failed or timed out")
        return
    
    if result["status"] == "complete":
        print("✓ Generation completed successfully!")
        
        # Save images
        images = result.get("images", [])
        if images:
            print(f"\n5. Saving {len(images)} generated image(s)...")
            saved_files = client.save_images(images)
            print(f"✓ Saved {len(saved_files)} image(s)")
        
        # Clear generation
        print(f"\n6. Clearing generation {generation_id}...")
        if client.clear_generation(generation_id):
            print("✓ Generation cleared from server memory")
        
    else:
        print(f"❌ Generation failed: {result.get('error', 'Unknown error')}")
    
    print("\n🎉 Example completed!")


if __name__ == "__main__":
    main()
