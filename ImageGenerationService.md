# Image Generation HTTP Service

A privacy-focused HTTP service for generating images using Stable Diffusion with CoreML on Apple Silicon.

## Features

- **Privacy Protection**: All prompts, negative prompts, and generated images are stored only in memory
- **Single Generation Limit**: Rejects new requests while one is in progress (no queue management)
- **Configurable**: Resource path and scheduler configurable via JSON file
- **RESTful API**: Simple HTTP endpoints for generation and status checking
- **Progress Tracking**: Real-time progress updates during generation

## Configuration

Create a `config.json` file with the following structure:

```json
{
  "resourcePath": "./Resources",
  "scheduler": "pndmScheduler",
  "port": 8080,
  "host": "127.0.0.1",
  "reduceMemory": false
}
```

### Configuration Options

- `resourcePath`: Path to directory containing Core ML models and tokenization resources
- `scheduler`: Scheduler type (`pndmScheduler`, `dpmSolverMultistepScheduler`, or `discreteFlowScheduler`)
- `port`: HTTP server port (default: 8080)
- `host`: HTTP server host (default: 127.0.0.1)
- `reduceMemory`: Enable reduced memory mode (recommended for iOS)

### Required Resources

The resource directory must contain:
- `TextEncoder.mlmodelc`
- `Unet.mlmodelc`
- `VAEDecoder.mlmodelc`
- `vocab.json`
- `merges.txt`

## API Endpoints

### POST /generate

Start a new image generation.

**Request Body:**
```json
{
  "prompt": "a beautiful landscape",
  "negativePrompt": "blurry, low quality",
  "imageCount": 1,
  "stepCount": 50,
  "seed": 12345,
  "guidanceScale": 7.5
}
```

**Response (202 Accepted):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "message": "Generation started"
}
```

**Error Response (409 Conflict):**
```json
{
  "error": "A generation is already in progress. Please wait for it to complete before starting a new one.",
  "code": 409
}
```

### GET /status/{id}

Check the status of a generation.

**Response (Pending):**
```json
{
  "status": "pending",
  "progress": {
    "currentStep": 25,
    "totalSteps": 50,
    "percentage": 50
  }
}
```

**Response (Complete):**
```json
{
  "status": "complete",
  "images": [
    "iVBORw0KGgoAAAANSUhEUgAA..." // base64 encoded PNG
  ]
}
```

**Response (Error):**
```json
{
  "status": "error",
  "error": "Generation failed: Invalid prompt"
}
```

### DELETE /generation/{id}

Clear a completed or errored generation from memory (privacy protection).

**Response (204 No Content)**

### GET /health

Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "service": "image-generation"
}
```

## Request Parameters

### GenerationRequest

- `prompt` (string, required): Text prompt to guide image generation
- `negativePrompt` (string, optional): Text prompt for what to avoid
- `imageCount` (integer, 1-4): Number of images to generate
- `stepCount` (integer, 1-100): Number of inference steps
- `seed` (integer): Random seed for reproducible results
- `guidanceScale` (float, 0.0-20.0): Controls prompt influence

## Building and Running

### Prerequisites

- macOS 13.1+ or iOS 16.2+
- Xcode 14.0+
- Swift 5.8+
- Core ML Stable Diffusion models

### Build

```bash
swift build -c release
```

### Run

```bash
swift run ImageGenerationService --config config.json --verbose
```

### Command Line Options

- `--config, -c`: Path to configuration file (default: config.json)
- `--port, -p`: Override port number
- `--host, -h`: Override host address
- `--verbose`: Enable verbose logging

## Privacy and Security

- **Memory-Only Storage**: Prompts and images are never written to disk
- **Automatic Cleanup**: Completed generations are cleared from memory
- **No Persistence**: Service restart clears all data
- **Single User**: Designed for single-user scenarios (no authentication)

## Error Handling

The service returns appropriate HTTP status codes:

- `200 OK`: Successful status check
- `202 Accepted`: Generation started
- `204 No Content`: Generation cleared
- `400 Bad Request`: Invalid request format or parameters
- `404 Not Found`: Generation ID not found
- `409 Conflict`: Generation already in progress
- `500 Internal Server Error`: Unexpected server error

## Example Usage

### Start Generation

```bash
curl -X POST http://localhost:8080/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a serene mountain landscape at sunset",
    "negativePrompt": "blurry, low quality",
    "imageCount": 1,
    "stepCount": 30,
    "seed": 42,
    "guidanceScale": 7.5
  }'
```

### Check Status

```bash
curl http://localhost:8080/status/550e8400-e29b-41d4-a716-************
```

### Clear Generation

```bash
curl -X DELETE http://localhost:8080/generation/550e8400-e29b-41d4-a716-************
```

## Performance Notes

- First generation may take longer due to model loading
- Memory usage depends on model size and `reduceMemory` setting
- Generation time varies with step count and image count
- Apple Silicon provides significant performance benefits

## Troubleshooting

### Common Issues

1. **Missing Models**: Ensure all required `.mlmodelc` files are present
2. **Memory Issues**: Enable `reduceMemory` in configuration
3. **Port Conflicts**: Change port in configuration or command line
4. **Resource Path**: Verify path is correct and accessible

### Logs

Enable verbose logging with `--verbose` flag for detailed information about:
- Configuration loading
- Resource validation
- Pipeline initialization
- Request processing
- Generation progress
