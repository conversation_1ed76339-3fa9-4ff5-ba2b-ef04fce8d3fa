// For licensing see accompanying LICENSE.md file.
// Copyright (C) 2024 Apple Inc. All Rights Reserved.

import Foundation
import CoreGraphics
import ImageIO
import UniformTypeIdentifiers

/// Request model for image generation
public struct GenerationRequest: Codable {
    /// Text prompt to guide image generation
    public let prompt: String

    /// Negative text prompt to guide what to avoid
    public let negativePrompt: String

    /// Number of images to generate (1-4)
    public let imageCount: Int

    /// Number of inference steps (1-100)
    public let stepCount: Int

    /// Random seed for reproducible results
    public let seed: UInt32

    /// Guidance scale (0.0-20.0)
    public let guidanceScale: Float

    public init(
        prompt: String,
        negativePrompt: String = "",
        imageCount: Int = 1,
        stepCount: Int = 50,
        seed: UInt32 = 0,
        guidanceScale: Float = 7.5
    ) {
        self.prompt = prompt
        self.negativePrompt = negativePrompt
        self.imageCount = imageCount
        self.stepCount = stepCount
        self.seed = seed
        self.guidanceScale = guidanceScale
    }
}

/// Response model for generation request
public struct GenerationResponse: Codable {
    /// Unique identifier for tracking the generation
    public let id: String

    /// Status message
    public let message: String

    public init(id: String, message: String = "Generation started") {
        self.id = id
        self.message = message
    }
}

/// Generation status enumeration
public enum GenerationStatus: String, Codable {
    case pending = "pending"
    case error = "error"
    case complete = "complete"
}

/// Response model for status check
public struct StatusResponse: Codable {
    /// Generation status
    public let status: GenerationStatus

    /// Optional error message if status is error
    public let error: String?

    /// Generated images as base64 encoded PNG data if status is complete
    public let images: [String]?

    /// Progress information (step/total steps)
    public let progress: ProgressInfo?

    public init(
        status: GenerationStatus,
        error: String? = nil,
        images: [String]? = nil,
        progress: ProgressInfo? = nil
    ) {
        self.status = status
        self.error = error
        self.images = images
        self.progress = progress
    }
}

/// Progress information for ongoing generation
public struct ProgressInfo: Codable {
    /// Current step
    public let currentStep: Int

    /// Total steps
    public let totalSteps: Int

    /// Progress percentage (0-100)
    public var percentage: Int {
        guard totalSteps > 0 else { return 0 }
        return Int((Float(currentStep) / Float(totalSteps)) * 100)
    }

    public init(currentStep: Int, totalSteps: Int) {
        self.currentStep = currentStep
        self.totalSteps = totalSteps
    }
}

/// Error response model
public struct ErrorResponse: Codable {
    /// Error message
    public let error: String

    /// HTTP status code
    public let code: Int

    public init(error: String, code: Int = 400) {
        self.error = error
        self.code = code
    }
}

/// In-memory generation state
public class GenerationState {
    public let id: String
    public let request: GenerationRequest
    public var status: GenerationStatus
    public var error: String?
    public var images: [CGImage]?
    public var progress: ProgressInfo?

    public init(id: String, request: GenerationRequest) {
        self.id = id
        self.request = request
        self.status = .pending
        self.error = nil
        self.images = nil
        self.progress = nil
    }

    /// Convert CGImages to base64 encoded PNG strings
    public func getBase64Images() -> [String]? {
        guard let images = images else { return nil }

        return images.compactMap { image in
            guard let data = image.pngData() else { return nil }
            return data.base64EncodedString()
        }
    }
}

/// Extension to convert CGImage to PNG data
extension CGImage {
    func pngData() -> Data? {
        let data = NSMutableData()
        guard let destination = CGImageDestinationCreateWithData(data, UTType.png.identifier as CFString, 1, nil) else {
            return nil
        }
        CGImageDestinationAddImage(destination, self, nil)
        guard CGImageDestinationFinalize(destination) else {
            return nil
        }
        return data as Data
    }
}

/// Request validation errors
public enum ValidationError: Error, LocalizedError {
    case emptyPrompt
    case invalidImageCount(Int)
    case invalidStepCount(Int)
    case invalidGuidanceScale(Float)

    public var errorDescription: String? {
        switch self {
        case .emptyPrompt:
            return "Prompt cannot be empty"
        case .invalidImageCount(let count):
            return "Image count must be between 1 and 4, got \(count)"
        case .invalidStepCount(let count):
            return "Step count must be between 1 and 100, got \(count)"
        case .invalidGuidanceScale(let scale):
            return "Guidance scale must be between 0.0 and 20.0, got \(scale)"
        }
    }
}

/// Request validation helper
public extension GenerationRequest {
    func validate() throws {
        if prompt.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw ValidationError.emptyPrompt
        }

        if imageCount < 1 || imageCount > 4 {
            throw ValidationError.invalidImageCount(imageCount)
        }

        if stepCount < 1 || stepCount > 100 {
            throw ValidationError.invalidStepCount(stepCount)
        }

        if guidanceScale < 0.0 || guidanceScale > 20.0 {
            throw ValidationError.invalidGuidanceScale(guidanceScale)
        }
    }
}
