// For licensing see accompanying LICENSE.md file.
// Copyright (C) 2024 Apple Inc. All Rights Reserved.

import Foundation
import Hummingbird
import HTTPTypes
import NIOCore

/// HTTP route handlers for the image generation service
public struct ImageGenerationRoutes {

    private let generationManager: GenerationManager

    public init(generationManager: GenerationManager) {
        self.generationManager = generationManager
    }

    /// Configure routes for the router
    public func configure(_ router: Router<some RequestContext>) {
        // Health check endpoint
        router.get("/health") { request, context in
            return ["status": "healthy", "service": "image-generation"]
        }

        // Generate images endpoint
        router.post("/generate") { request, context in
            return try await self.handleGenerate(request)
        }

        // Check generation status endpoint
        router.get("/generation/:id") { request, context in
            return try await self.handleStatus(request, context: context)
        }

        // Clear completed generation endpoint (for privacy)
        router.delete("/generation/:id") { request, context in
            return try await self.handleClear(request, context: context)
        }
    }

    /// Handle image generation request
    private func handleGenerate(_ request: Request) async throws -> Response {
        do {
            // Parse request body
            let bodyData = try await request.body.collect(upTo: .max)
            let generationRequest = try JSONDecoder().decode(GenerationRequest.self, from: Data(bodyData.readableBytesView))

            // Start generation
            let id = try await generationManager.startGeneration(generationRequest)

            // Log generation request
            print("🎨 Generation requested: \(id)")

            // Return response
            let response = GenerationResponse(id: id)
            let responseData = try JSONEncoder().encode(response)

            return Response(
                status: .accepted,
                headers: [.contentType: "application/json"],
                body: .init(contentLength: responseData.count) { writer in
                    let buffer = ByteBuffer(data: responseData)
                    try await writer.write(buffer)
                    try await writer.finish(nil)
                }
            )

        } catch let error as ValidationError {
            return try createErrorResponse(error: error.localizedDescription, status: .badRequest)
        } catch let error as GenerationError {
            return try createErrorResponse(error: error.localizedDescription, status: .conflict)
        } catch {
            return try createErrorResponse(error: "Invalid request format", status: .badRequest)
        }
    }

    /// Handle status check request
    private func handleStatus(_ request: Request, context: some RequestContext) async throws -> Response {
        guard let id = context.parameters.get("id", as: String.self) else {
            throw HTTPError(.badRequest, message: "Missing generation ID")
        }

        do {
            // Get status without blocking on the generation
            let statusResponse = try generationManager.getNonBlockingStatus(for: id)
            let responseData = try JSONEncoder().encode(statusResponse)

            return Response(
                status: .ok,
                headers: [.contentType: "application/json"],
                body: .init(contentLength: responseData.count) { writer in
                    let buffer = ByteBuffer(data: responseData)
                    try await writer.write(buffer)
                    try await writer.finish(nil)
                }
            )

        } catch let error as GenerationError {
            return try createErrorResponse(error: error.localizedDescription, status: .notFound)
        } catch {
            return try createErrorResponse(error: "Internal server error", status: .internalServerError)
        }
    }

    /// Handle clear generation request
    private func handleClear(_ request: Request, context: some RequestContext) async throws -> Response {
        guard let id = context.parameters.get("id", as: String.self) else {
            throw HTTPError(.badRequest, message: "Missing generation ID")
        }

        await generationManager.clearGeneration(id: id)

        return Response(
            status: .noContent,
            headers: [:],
            body: .init()
        )
    }

    /// Create error response
    private func createErrorResponse(error: String, status: HTTPResponse.Status) throws -> Response {
        let errorResponse = ErrorResponse(error: error, code: Int(status.code))
        let responseData = try JSONEncoder().encode(errorResponse)

        return Response(
            status: status,
            headers: [.contentType: "application/json"],
            body: .init(contentLength: responseData.count) { writer in
                let buffer = ByteBuffer(data: responseData)
                try await writer.write(buffer)
                try await writer.finish(nil)
            }
        )
    }
}

/// Middleware for logging requests
public struct LoggingMiddleware<Context: RequestContext>: RouterMiddleware {
    public init() {}

    public func handle(_ request: Request, context: Context, next: (Request, Context) async throws -> Response) async throws -> Response {
        let startTime = Date()

        print("[\(formatDate(startTime))] \(request.method) \(request.uri.path)")

        let response = try await next(request, context)

        let duration = Date().timeIntervalSince(startTime)
        print("[\(formatDate(Date()))] \(request.method) \(request.uri.path) - \(response.status.code) (\(String(format: "%.2f", duration * 1000))ms)")

        return response
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
}

/// CORS middleware for cross-origin requests
public struct CORSMiddleware<Context: RequestContext>: RouterMiddleware {
    public init() {}

    public func handle(_ request: Request, context: Context, next: (Request, Context) async throws -> Response) async throws -> Response {
        // Handle preflight requests
        if request.method == .options {
            return Response(
                status: .ok,
                headers: [
                    .accessControlAllowOrigin: "*",
                    .accessControlAllowMethods: "GET, POST, DELETE, OPTIONS",
                    .accessControlAllowHeaders: "Content-Type, Authorization",
                    .accessControlMaxAge: "86400"
                ],
                body: .init()
            )
        }

        let response = try await next(request, context)

        // Add CORS headers to response
        var headers = response.headers
        headers[.accessControlAllowOrigin] = "*"
        headers[.accessControlAllowMethods] = "GET, POST, DELETE, OPTIONS"
        headers[.accessControlAllowHeaders] = "Content-Type, Authorization"

        return Response(
            status: response.status,
            headers: headers,
            body: response.body
        )
    }
}

/// Extension for HTTP header names
extension HTTPField.Name {
    static let accessControlAllowOrigin = HTTPField.Name("Access-Control-Allow-Origin")!
    static let accessControlAllowMethods = HTTPField.Name("Access-Control-Allow-Methods")!
    static let accessControlAllowHeaders = HTTPField.Name("Access-Control-Allow-Headers")!
    static let accessControlMaxAge = HTTPField.Name("Access-Control-Max-Age")!
}
