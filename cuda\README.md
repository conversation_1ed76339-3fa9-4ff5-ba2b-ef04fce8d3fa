# CUDA SDXL Image Generation with k-diffusion

This directory contains PyTorch-based SDXL image generation scripts that utilize CUDA for GPU acceleration and k-diffusion for advanced sampling methods.

## Files

- `image_generator.py` - Core image generation class using k-diffusion
- `api_server.py` - FastAPI server for image generation
- `generation_manager.py` - Async generation management
- `models.py` - Pydantic models for API requests/responses
- `run_server.py` - Server startup script

## Requirements

The script uses k-diffusion and related dependencies:
- `torch` - PyTorch framework
- `k-diffusion` - Advanced diffusion sampling library
- `einops` - Tensor operations
- `kornia` - Computer vision library
- `torchdiffeq` - Differential equation solvers
- `torchsde` - Stochastic differential equations
- `clean-fid` - FID computation
- `clip-anytorch` - CLIP model support
- `dctorch` - Discrete cosine transform
- `jsonmerge` - JSON merging utilities
- `scikit-image` - Image processing
- `safetensors` - For loading safetensors model files
- `fastapi` - Web API framework
- `uvicorn` - ASGI server

## Usage

### Starting the API Server

Start the FastAPI server with a model:

```bash
python cuda/run_server.py --model-path /path/to/your/sdxl_model.safetensors
```

### Server Options

- `--model-path`: Path to the SDXL or Pony safetensors file
- `--host`: Host to bind the server to (default: 127.0.0.1)
- `--port`: Port to bind the server to (default: 8074)
- `--device`: Device to use for generation (auto, cuda, cpu)
- `--reload`: Enable auto-reload for development

### API Endpoints

Once the server is running, you can access:

- **POST /generate** - Generate images
- **GET /generation/{id}** - Check generation status
- **GET /health** - Server health check
- **GET /model** - Model information
- **GET /docs** - Interactive API documentation

### Example API Usage

Generate an image using curl:

```bash
curl -X POST "http://localhost:8074/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a beautiful landscape with mountains and a lake",
    "negativePrompt": "blurry, low quality",
    "stepCount": 30,
    "guidanceScale": 7.5,
    "imageCount": 1,
    "seed": 42,
    "height": 1024,
    "width": 1024
  }'
```

### k-diffusion Sampling

The system now uses k-diffusion's advanced sampling methods:
- **DPM++ 2M** sampler for high-quality results
- **Karras noise schedule** for improved sampling
- **Optimized sigma scheduling** for better convergence
- **Progress callbacks** for real-time generation tracking

## Output

Generated images are returned as base64-encoded PNG strings through the API. The API provides:
- Real-time progress updates during generation
- Unique generation IDs for tracking
- Comprehensive error handling and logging

## System Requirements

- NVIDIA GPU with CUDA support (recommended: 8GB+ VRAM for SDXL)
- PyTorch with CUDA support installed
- k-diffusion library and dependencies
- Python 3.8+

## k-diffusion Features

### Advanced Sampling Methods
- **DPM++ 2M**: High-quality sampler with excellent speed/quality tradeoff
- **Euler**: Fast and stable sampling method
- **LMS**: Linear multistep method for smooth results
- **Karras scheduling**: Improved noise schedule for better convergence

### Model Support
- **SDXL models**: Full Stable Diffusion XL support
- **Pony Diffusion**: Enhanced support with quality tags
- **Custom models**: Any SDXL-compatible safetensors model
- **Automatic detection**: Pony vs SDXL model type detection

### Performance Optimizations
- **Device-aware tensor management**: Automatic GPU/CPU tensor placement
- **Memory-efficient generation**: Optimized memory usage patterns
- **Progress tracking**: Real-time step-by-step progress reporting
- **Batch processing**: Support for multiple image generation

## Troubleshooting

### Out of Memory Errors
- Try using `--device cpu` flag
- Close other GPU-intensive applications
- Reduce step count or image dimensions
- Use the API's memory management endpoints

### Model Loading Issues
- Ensure the safetensors file is a valid SDXL/Pony model
- Check file permissions and path correctness
- Verify model compatibility with k-diffusion

### k-diffusion Installation Issues
- Install from source: `pip install git+https://github.com/crowsonkb/k-diffusion.git`
- Ensure all dependencies are installed: `pip install -r requirements.txt`
- Check for CUDA compatibility issues

### CUDA Not Available
- Install PyTorch with CUDA support: `pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118`
- Check NVIDIA driver installation
- Verify CUDA toolkit installation

## Memory Management

The image generation service includes comprehensive memory management to prevent memory leaks:

### Automatic Memory Cleanup
- GPU memory is automatically cleared after each generation
- PIL images are properly closed and deleted after encoding
- Intermediate tensors and results are cleaned up
- Garbage collection is triggered after cleanup operations

### Manual Memory Management
- Use `POST /cleanup` to force cleanup of all completed generations
- Use `DELETE /generation/{id}` to clear specific completed generations
- Monitor memory usage through the `/status` endpoint

### Memory Monitoring
- Memory usage is logged before and after each generation
- Device information includes current memory allocation
- CUDA cache is cleared automatically after each generation
