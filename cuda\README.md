# CUDA SDXL Image Generation

This directory contains PyTorch-based SDXL image generation scripts that utilize CUDA for GPU acceleration.

## Files

- `generate_image.py` - Basic SDXL image generation script

## Requirements

The script uses the dependencies already listed in the main `requirements.txt` file:
- `torch` - PyTorch framework
- `diffusers[torch]==0.30.2` - Hugging Face diffusers library
- `safetensors` - For loading safetensors model files

## Usage

### Basic Usage

Generate an image with the default prompt "mountain at sunset":

```bash
python generate_image.py --model-path /path/to/your/sdxl_model.safetensors
```

### Custom Prompt

Generate an image with a custom prompt:

```bash
python generate_image.py --model-path /path/to/your/sdxl_model.safetensors --prompt "a beautiful landscape with mountains and a lake"
```

### Auto-detect Model

If you have a .safetensors file in the current directory or subdirectories, the script can auto-detect it:

```bash
python generate_image.py --prompt "mountain at sunset"
```

### Force CPU Usage

To use CPU instead of CUDA (useful for testing or if you have GPU memory issues):

```bash
python generate_image.py --model-path /path/to/your/sdxl_model.safetensors --cpu
```

### Custom Output Directory

Specify a custom output directory:

```bash
python generate_image.py --model-path /path/to/your/sdxl_model.safetensors --output-dir ./my_images
```

## Command Line Options

- `--model-path`: Path to the SDXL safetensors file (optional if file can be auto-detected)
- `--prompt`: Text prompt for image generation (default: "mountain at sunset")
- `--output-dir`: Output directory for generated images (default: "output")
- `--cpu`: Force CPU usage even if CUDA is available

## Output

Generated images are saved as PNG files with timestamps in the filename:
- Format: `sdxl_generation_YYYYMMDD_HHMMSS.png`
- Default location: `./output/` directory

## CUDA Requirements

- NVIDIA GPU with CUDA support
- PyTorch with CUDA support installed
- Sufficient GPU memory (recommended: 8GB+ VRAM for SDXL)

## Troubleshooting

### Out of Memory Errors
- Try using the `--cpu` flag
- Close other GPU-intensive applications
- Use a smaller model or reduce batch size
- Use the `/cleanup` endpoint to force memory cleanup between generations
- Clear completed generations using the `/generation/{id}` DELETE endpoint

### Model Loading Issues
- Ensure the safetensors file is a valid SDXL model
- Check file permissions
- Verify the file path is correct

### CUDA Not Available
- Install PyTorch with CUDA support: `pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118`
- Check NVIDIA driver installation
- Verify CUDA toolkit installation

## Memory Management

The image generation service includes comprehensive memory management to prevent memory leaks:

### Automatic Memory Cleanup
- GPU memory is automatically cleared after each generation
- PIL images are properly closed and deleted after encoding
- Intermediate tensors and results are cleaned up
- Garbage collection is triggered after cleanup operations

### Manual Memory Management
- Use `POST /cleanup` to force cleanup of all completed generations
- Use `DELETE /generation/{id}` to clear specific completed generations
- Monitor memory usage through the `/status` endpoint

### Memory Monitoring
- Memory usage is logged before and after each generation
- Device information includes current memory allocation
- CUDA cache is cleared automatically after each generation
