#!/usr/bin/env python3
"""
Test script to verify that the API server can be imported correctly.
"""

try:
    print("Testing imports...")
    
    # Test basic imports
    from cuda.models import GenerationRequest, GenerationResponse, StatusResponse
    print("✓ Models imported successfully")
    
    from cuda.image_generator import ImageGenerator, find_safetensors_file
    print("✓ Image generator imported successfully")
    
    from cuda.generation_manager import GenerationManager
    print("✓ Generation manager imported successfully")
    
    # Test creating a sample request
    request = GenerationRequest(
        prompt="test prompt",
        negativePrompt="",
        imageCount=1,
        stepCount=20,
        seed=42,
        guidanceScale=7.5
    )
    print("✓ GenerationRequest created successfully")
    
    # Test finding safetensors file
    model_file = find_safetensors_file(".")
    if model_file:
        print(f"✓ Found model file: {model_file}")
    else:
        print("⚠ No model file found (this is expected if no .safetensors file is present)")
    
    print("\n🎉 All imports successful! The API server should work correctly.")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure all dependencies are installed: pip install -r cuda/requirements.txt")
    
except Exception as e:
    print(f"❌ Error: {e}")
    
print("\nTo start the server, run:")
print("python cuda/run_server.py")
print("or")
print("python -m cuda.api_server")
