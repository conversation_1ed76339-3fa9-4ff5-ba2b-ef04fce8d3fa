# Windows/CUDA-specific requirements for PyTorch SDXL image generation
# This file excludes macOS-specific packages like coremltools and diffusionkit

# Core PyTorch with CUDA support
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Diffusers for Stable Diffusion models
diffusers[torch]==0.30.2

# Model loading and tokenization
transformers==4.44.2
safetensors

# Image processing and utilities
Pillow>=9.0.0
numpy>=1.21.0

# Optional utilities
matplotlib
scipy
scikit-learn

# For testing
pytest

# FastAPI web server dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
