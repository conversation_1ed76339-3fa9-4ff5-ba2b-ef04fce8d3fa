#!/usr/bin/env python3
"""
Image generation module for SDXL using k-diffusion and PyTorch.
Refactored from diffusers-based implementation to use k-diffusion samplers.
Supports both regular SDXL and Pony Diffusion models.
"""

import torch
import os
import base64
import io
import gc
import math
from pathlib import Path
from diffusers import StableDiffusionXLPipeline
from PIL import Image
from typing import Optional, List, Callable, Literal
import k_diffusion as K
from k_diffusion import sampling, utils


# Set environment variable to disable symlinks warning on Windows
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"

# Model type definitions
ModelType = Literal["sdxl", "pony"]


class DiffusersSDXLDenoiser(torch.nn.Module):
    """A k-diffusion compatible wrapper for diffusers SDXL UNet models."""

    def __init__(self, pipeline, sigma_data=1.0):
        super().__init__()
        self.pipeline = pipeline
        self.unet = pipeline.unet
        self.vae = pipeline.vae
        self.text_encoder = pipeline.text_encoder
        self.text_encoder_2 = pipeline.text_encoder_2
        self.tokenizer = pipeline.tokenizer
        self.tokenizer_2 = pipeline.tokenizer_2
        self.scheduler = pipeline.scheduler
        self.sigma_data = sigma_data

        # Get the scheduler's alphas_cumprod for noise schedule conversion
        if hasattr(self.scheduler, 'alphas_cumprod'):
            self.alphas_cumprod = self.scheduler.alphas_cumprod.to(self.unet.device)
        else:
            # Fallback for schedulers without alphas_cumprod
            self.alphas_cumprod = torch.linspace(0.9999, 0.0001, 1000).to(self.unet.device)

    def sigma_to_t(self, sigma):
        """Convert k-diffusion sigma to diffusers timestep."""
        # Ensure sigma is on the same device as alphas_cumprod
        if isinstance(sigma, torch.Tensor):
            sigma = sigma.to(self.alphas_cumprod.device)
        else:
            sigma = torch.tensor(sigma, device=self.alphas_cumprod.device)

        # Convert sigma to alpha_cumprod
        alpha_cumprod = 1 / (sigma ** 2 + 1)

        # Find the closest timestep
        diffs = torch.abs(self.alphas_cumprod - alpha_cumprod)
        timestep = torch.argmin(diffs)
        return timestep.float().to(self.alphas_cumprod.device)

    def get_scalings(self, sigma):
        """Get the input/output scalings for the denoiser."""
        c_skip = self.sigma_data ** 2 / (sigma ** 2 + self.sigma_data ** 2)
        c_out = sigma * self.sigma_data / (sigma ** 2 + self.sigma_data ** 2) ** 0.5
        c_in = 1 / (sigma ** 2 + self.sigma_data ** 2) ** 0.5
        return c_skip, c_out, c_in

    def encode_prompt(self, prompt, negative_prompt="", device="cuda"):
        """Encode text prompts using SDXL text encoders."""
        # Tokenize prompts
        text_inputs = self.tokenizer(
            prompt,
            padding="max_length",
            max_length=self.tokenizer.model_max_length,
            truncation=True,
            return_tensors="pt",
        )
        text_input_ids = text_inputs.input_ids.to(device)

        text_inputs_2 = self.tokenizer_2(
            prompt,
            padding="max_length",
            max_length=self.tokenizer_2.model_max_length,
            truncation=True,
            return_tensors="pt",
        )
        text_input_ids_2 = text_inputs_2.input_ids.to(device)

        # Encode with first text encoder
        prompt_embeds = self.text_encoder(text_input_ids, output_hidden_states=True)
        pooled_prompt_embeds = prompt_embeds[0]
        prompt_embeds = prompt_embeds.hidden_states[-2]

        # Encode with second text encoder
        prompt_embeds_2 = self.text_encoder_2(text_input_ids_2, output_hidden_states=True)
        pooled_prompt_embeds_2 = prompt_embeds_2[0]
        prompt_embeds_2 = prompt_embeds_2.hidden_states[-2]

        # Concatenate embeddings
        prompt_embeds = torch.concat([prompt_embeds, prompt_embeds_2], dim=-1)

        # Handle negative prompts
        if negative_prompt:
            uncond_tokens = self.tokenizer(
                negative_prompt,
                padding="max_length",
                max_length=self.tokenizer.model_max_length,
                truncation=True,
                return_tensors="pt",
            )
            uncond_input_ids = uncond_tokens.input_ids.to(device)

            uncond_tokens_2 = self.tokenizer_2(
                negative_prompt,
                padding="max_length",
                max_length=self.tokenizer_2.model_max_length,
                truncation=True,
                return_tensors="pt",
            )
            uncond_input_ids_2 = uncond_tokens_2.input_ids.to(device)

            negative_prompt_embeds = self.text_encoder(uncond_input_ids, output_hidden_states=True)
            negative_pooled_prompt_embeds = negative_prompt_embeds[0]
            negative_prompt_embeds = negative_prompt_embeds.hidden_states[-2]

            negative_prompt_embeds_2 = self.text_encoder_2(uncond_input_ids_2, output_hidden_states=True)
            negative_pooled_prompt_embeds_2 = negative_prompt_embeds_2[0]
            negative_prompt_embeds_2 = negative_prompt_embeds_2.hidden_states[-2]

            negative_prompt_embeds = torch.concat([negative_prompt_embeds, negative_prompt_embeds_2], dim=-1)
        else:
            negative_prompt_embeds = torch.zeros_like(prompt_embeds)
            negative_pooled_prompt_embeds_2 = torch.zeros_like(pooled_prompt_embeds_2)

        return prompt_embeds, negative_prompt_embeds, pooled_prompt_embeds_2, negative_pooled_prompt_embeds_2

    def forward(self, x, sigma, prompt_embeds, negative_prompt_embeds, pooled_prompt_embeds, negative_pooled_prompt_embeds, guidance_scale=7.5):
        """Forward pass through the denoiser."""
        # Ensure all inputs are on the same device as x
        device = x.device

        # Move sigma to correct device if needed
        if isinstance(sigma, torch.Tensor):
            sigma = sigma.to(device)
        else:
            sigma = torch.tensor(sigma, device=device)

        # Move embeddings to correct device
        prompt_embeds = prompt_embeds.to(device)
        negative_prompt_embeds = negative_prompt_embeds.to(device)
        pooled_prompt_embeds = pooled_prompt_embeds.to(device)
        negative_pooled_prompt_embeds = negative_pooled_prompt_embeds.to(device)

        c_skip, c_out, c_in = [utils.append_dims(x, x.ndim) for x in self.get_scalings(sigma)]

        # Scale input
        x_in = x * c_in

        # Convert sigma to timestep
        t = self.sigma_to_t(sigma)
        if t.dim() == 0:
            t = t.unsqueeze(0)
        t = t.expand(x.shape[0]).to(device)

        # Prepare embeddings for classifier-free guidance
        if guidance_scale > 1.0:
            # Concatenate negative and positive embeddings
            encoder_hidden_states = torch.cat([negative_prompt_embeds, prompt_embeds])
            pooled_prompt_embeds_combined = torch.cat([negative_pooled_prompt_embeds, pooled_prompt_embeds])
            x_in = torch.cat([x_in, x_in])
            t = torch.cat([t, t])
        else:
            encoder_hidden_states = prompt_embeds
            pooled_prompt_embeds_combined = pooled_prompt_embeds

        # Get time embeddings and add pooled embeddings
        added_cond_kwargs = {"text_embeds": pooled_prompt_embeds_combined, "time_ids": torch.zeros((x_in.shape[0], 6), device=device)}

        # UNet forward pass
        noise_pred = self.unet(
            x_in,
            t,
            encoder_hidden_states=encoder_hidden_states,
            added_cond_kwargs=added_cond_kwargs,
            return_dict=False,
        )[0]

        # Apply classifier-free guidance
        if guidance_scale > 1.0:
            noise_pred_uncond, noise_pred_text = noise_pred.chunk(2)
            noise_pred = noise_pred_uncond + guidance_scale * (noise_pred_text - noise_pred_uncond)

        # Apply output scaling
        return x * c_skip + noise_pred * c_out


class ImageGenerator:
    """SDXL Image Generator class for handling pipeline and generation"""

    def __init__(self, model_path: str, device: str = "auto"):
        """
        Initialize the image generator.

        Args:
            model_path: Path to the SDXL safetensors file
            device: Device to use ("cuda", "cpu", or "auto")
        """
        self.model_path = model_path
        self.device = self._determine_device(device)
        self.model_type = self._detect_model_type(model_path)
        self.pipeline = None
        self.k_model = None
        self._load_pipeline()
    
    def _determine_device(self, device: str) -> str:
        """Determine the best device to use"""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device

    def _detect_model_type(self, model_path: str) -> ModelType:
        """
        Detect the model type based on filename patterns.

        Args:
            model_path: Path to the model file

        Returns:
            Model type ("sdxl" or "pony")
        """
        filename = Path(model_path).name.lower()

        # Check for Pony model indicators in filename
        pony_indicators = [
            "pony", "ponydiffusion", "pony_diffusion", "ponyv6", "pony_v6",
            "pdxl", "pony-diffusion", "ponydiff", "ponyxl"
        ]

        for indicator in pony_indicators:
            if indicator in filename:
                print(f"🐴 Detected Pony model: {filename}")
                return "pony"

        print(f"🎨 Detected SDXL model: {filename}")
        return "sdxl"
    
    def _load_pipeline(self):
        """Load the SDXL pipeline from the safetensors file"""
        model_type_name = "Pony Diffusion" if self.model_type == "pony" else "SDXL"
        print(f"Loading {model_type_name} model from: {self.model_path}")

        # Verify model file exists
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Model file not found: {self.model_path}")

        # Prepare pipeline loading arguments
        pipeline_args = {
            "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
            "use_safetensors": True,
            "safety_checker": None,
            "feature_extractor": None
        }

        # Add Pony-specific configurations
        if self.model_type == "pony":
            # Pony models work better with CLIP skip 2
            pipeline_args["clip_skip"] = 2
            print("🐴 Applying Pony model optimizations (CLIP skip 2)")

        # First try offline mode
        try:
            print("Attempting to load pipeline in offline mode...")

            # Set offline mode to prevent any downloads
            os.environ["HF_HUB_OFFLINE"] = "1"
            os.environ["TRANSFORMERS_OFFLINE"] = "1"

            self.pipeline = StableDiffusionXLPipeline.from_single_file(
                self.model_path,
                local_files_only=True,
                **pipeline_args
            )

            print("✓ Successfully loaded in offline mode!")

        except Exception as offline_error:
            print(f"Offline mode failed: {offline_error}")
            print("Trying with minimal online components (config files only)...")

            # Clear offline environment variables
            if "HF_HUB_OFFLINE" in os.environ:
                del os.environ["HF_HUB_OFFLINE"]
            if "TRANSFORMERS_OFFLINE" in os.environ:
                del os.environ["TRANSFORMERS_OFFLINE"]

            try:
                self.pipeline = StableDiffusionXLPipeline.from_single_file(
                    self.model_path,
                    **pipeline_args
                )
                print("✓ Successfully loaded with minimal online components!")

            except Exception as online_error:
                raise RuntimeError(f"Failed to load pipeline: {online_error}")

        # Move to the appropriate device and optimize
        self._setup_pipeline()

        # Create k-diffusion wrapper
        self._setup_k_diffusion()
    
    def _setup_pipeline(self):
        """Setup and optimize the pipeline"""
        try:
            # Move to the appropriate device
            print(f"Moving pipeline to {self.device}...")
            self.pipeline = self.pipeline.to(self.device)
            
            # Enable memory efficient settings
            if self.device == "cuda":
                print("Enabling CUDA optimizations...")
                self.pipeline.enable_attention_slicing()
                try:
                    self.pipeline.enable_model_cpu_offload()
                except Exception as e:
                    print(f"Warning: Could not enable model CPU offload: {e}")
            else:
                print("Using CPU optimizations...")
                self.pipeline.enable_attention_slicing()
            
            print(f"Pipeline loaded successfully on {self.device}")
            
        except Exception as e:
            raise RuntimeError(f"Error setting up pipeline: {e}")

    def _setup_k_diffusion(self):
        """Setup k-diffusion wrapper for the pipeline"""
        try:
            print("Setting up k-diffusion wrapper...")

            # Create the k-diffusion denoiser wrapper
            self.k_model = DiffusersSDXLDenoiser(self.pipeline, sigma_data=1.0)

            print("✓ k-diffusion wrapper created successfully")

        except Exception as e:
            raise RuntimeError(f"Error setting up k-diffusion wrapper: {e}")
    
    def _enhance_prompt_for_pony(self, prompt: str) -> str:
        """
        Enhance prompt for Pony models by adding quality score tags if not present.

        Args:
            prompt: Original prompt

        Returns:
            Enhanced prompt with quality tags
        """
        if self.model_type != "pony":
            return prompt

        # Check if quality scores are already present
        quality_indicators = ["score_"]
        has_quality_tags = any(indicator in prompt.lower() for indicator in quality_indicators)

        if not has_quality_tags:
            # Add quality score tags at the beginning
            quality_prefix = "score_9, score_8_up, score_7_up, "
            enhanced_prompt = quality_prefix + prompt
            print("🐴 Enhanced Pony prompt with quality tags")
            return enhanced_prompt

        return prompt

    def _get_default_guidance_scale(self) -> float:
        """Get default guidance scale based on model type"""
        if self.model_type == "pony":
            return 4.5  # Pony models work better with lower guidance scale
        return 7.5  # Standard SDXL default

    def generate_images(
        self,
        prompt: str,
        negative_prompt: str = "",
        image_count: int = 1,
        step_count: int = 20,
        guidance_scale: float = 7.5,
        seed: int = 0,
        height: int = 1024,
        width: int = 1024,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[str]:
        """
        Generate images using k-diffusion sampling and return them as base64 encoded PNG strings.

        Args:
            prompt: Text prompt to guide image generation
            negative_prompt: Negative prompt to guide what to avoid
            image_count: Number of images to generate
            step_count: Number of inference steps
            guidance_scale: Guidance scale for generation
            seed: Random seed for reproducible results
            height: Image height in pixels
            width: Image width in pixels
            progress_callback: Optional callback for progress updates (current_step, total_steps)

        Returns:
            List of base64 encoded PNG image strings
        """
        if not self.k_model:
            raise RuntimeError("k-diffusion model not loaded")

        # Enhance prompt for Pony models
        enhanced_prompt = self._enhance_prompt_for_pony(prompt)

        # Use model-specific default guidance scale if not explicitly set
        if guidance_scale == 7.5:  # Default value, use model-specific default
            guidance_scale = self._get_default_guidance_scale()

        print(f"🤖 Generating {image_count} image(s) with {self.model_type.upper()} model using k-diffusion")
        if enhanced_prompt != prompt:
            print("🐴 Using enhanced prompt")

        # Log memory usage before generation
        if self.device == "cuda":
            self._log_memory_usage("Before generation")

        # Set seed for reproducibility
        if seed > 0:
            torch.manual_seed(seed)
            if self.device == "cuda":
                torch.cuda.manual_seed(seed)

        base64_images = []
        latents_list = []

        try:
            # Encode prompts
            prompt_embeds, negative_prompt_embeds, pooled_prompt_embeds, negative_pooled_prompt_embeds = \
                self.k_model.encode_prompt(enhanced_prompt, negative_prompt, self.device)

            # Generate latents for each image
            latent_channels = 4  # SDXL uses 4 channels
            latent_height = height // 8  # VAE downsamples by 8x
            latent_width = width // 8

            for i in range(image_count):
                # Create random latents
                latents = torch.randn(
                    (1, latent_channels, latent_height, latent_width),
                    device=self.device,
                    dtype=torch.float16 if self.device == "cuda" else torch.float32
                )
                latents_list.append(latents)

            # Set up k-diffusion sampling parameters
            sigma_min = 0.0292
            sigma_max = 14.6146

            # Create noise schedule using Karras schedule
            sigmas = K.sampling.get_sigmas_karras(step_count, sigma_min, sigma_max, device=self.device)

            # Create progress callback for k-diffusion
            def k_diffusion_callback(info):
                if progress_callback:
                    step = info.get('i', 0)
                    progress_callback(step + 1, step_count)

            # Generate images using k-diffusion sampling
            with torch.no_grad():
                for i, latents in enumerate(latents_list):
                    print(f"🎨 Generating image {i+1}/{image_count}...")

                    # Scale initial noise
                    latents = latents * sigmas[0]

                    # Create denoiser function for this specific prompt
                    def denoiser(x, sigma):
                        return self.k_model(
                            x, sigma,
                            prompt_embeds, negative_prompt_embeds,
                            pooled_prompt_embeds, negative_pooled_prompt_embeds,
                            guidance_scale
                        )

                    # Sample using DPM++ 2M sampler (good quality/speed tradeoff)
                    denoised_latents = K.sampling.sample_dpmpp_2m(
                        denoiser,
                        latents,
                        sigmas,
                        callback=k_diffusion_callback if i == 0 else None  # Only show progress for first image
                    )

                    # Decode latents to image
                    with torch.no_grad():
                        # Scale latents for VAE
                        denoised_latents = denoised_latents / 0.13025

                        # Decode using VAE
                        image = self.k_model.vae.decode(denoised_latents).sample

                        # Convert to PIL Image
                        image = (image / 2 + 0.5).clamp(0, 1)
                        image = image.cpu().permute(0, 2, 3, 1).float().numpy()
                        image = (image * 255).round().astype("uint8")[0]
                        pil_image = Image.fromarray(image)

                        # Convert to base64
                        base64_str = self._image_to_base64(pil_image)
                        base64_images.append(base64_str)
                        print(f"✓ Image {i+1}/{image_count} generated and encoded")

                        # Clean up
                        pil_image.close()
                        del pil_image, image, denoised_latents

            return base64_images

        except Exception as e:
            # Clean up on error
            self._cleanup_generation_memory()
            raise RuntimeError(f"Error during k-diffusion image generation: {e}")
        finally:
            # Always clean up memory after generation
            self._cleanup_generation_memory()

            # Log memory usage after cleanup
            if self.device == "cuda":
                self._log_memory_usage("After cleanup")
    
    # Note: Progress callback is now handled directly in k-diffusion sampling
    
    def _image_to_base64(self, image: Image.Image) -> str:
        """Convert PIL Image to base64 encoded PNG string"""
        buffer = io.BytesIO()
        try:
            image.save(buffer, format="PNG")
            buffer.seek(0)
            image_bytes = buffer.getvalue()
            base64_str = base64.b64encode(image_bytes).decode('utf-8')
            return base64_str
        finally:
            # Ensure buffer is properly closed
            buffer.close()

    def _cleanup_generation_memory(self, result=None):
        """Clean up memory after image generation"""
        try:
            # Clear the result object if it exists
            if result is not None:
                # Clear any remaining images
                if hasattr(result, 'images') and result.images:
                    for img in result.images:
                        if hasattr(img, 'close'):
                            img.close()
                    result.images.clear()
                del result

            # Force garbage collection
            gc.collect()

            # Clear CUDA cache if using GPU
            if self.device == "cuda" and torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

        except Exception as e:
            print(f"Warning: Error during memory cleanup: {e}")

    def _log_memory_usage(self, stage: str):
        """Log current memory usage for debugging"""
        if self.device == "cuda" and torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3
            reserved = torch.cuda.memory_reserved() / 1024**3
            print(f"🧠 Memory {stage}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model"""
        return {
            "model_path": self.model_path,
            "model_type": self.model_type,
            "model_name": Path(self.model_path).name,
            "default_guidance_scale": self._get_default_guidance_scale(),
            "supports_quality_tags": self.model_type == "pony"
        }

    def get_device_info(self) -> dict:
        """Get information about the current device"""
        info = {
            "device": self.device,
            "cuda_available": torch.cuda.is_available()
        }

        if torch.cuda.is_available():
            info.update({
                "device_count": torch.cuda.device_count(),
                "current_device": torch.cuda.current_device(),
                "device_name": torch.cuda.get_device_name(torch.cuda.current_device()),
                "cuda_version": torch.version.cuda,
                "memory_allocated_gb": torch.cuda.memory_allocated() / 1024**3,
                "memory_reserved_gb": torch.cuda.memory_reserved() / 1024**3
            })

        return info

    def cleanup(self):
        """Clean up the pipeline and free GPU memory"""
        try:
            if self.k_model is not None:
                # Delete the k-diffusion model
                del self.k_model
                self.k_model = None

            if self.pipeline is not None:
                # Move pipeline to CPU to free GPU memory
                if self.device == "cuda":
                    print("Moving pipeline to CPU for cleanup...")
                    self.pipeline = self.pipeline.to("cpu")

                # Delete the pipeline
                del self.pipeline
                self.pipeline = None

            # Force cleanup
            gc.collect()
            if self.device == "cuda" and torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

            print("✓ ImageGenerator cleanup completed")

        except Exception as e:
            print(f"Warning: Error during ImageGenerator cleanup: {e}")

    def __del__(self):
        """Destructor to ensure cleanup when object is deleted"""
        self.cleanup()


def find_safetensors_file(search_dir: str = ".") -> Optional[str]:
    """Find the first .safetensors file in the given directory"""
    search_path = Path(search_dir)
    safetensors_files = list(search_path.rglob("*.safetensors"))
    
    if not safetensors_files:
        return None
    
    # Return the first one found
    return str(safetensors_files[0])
